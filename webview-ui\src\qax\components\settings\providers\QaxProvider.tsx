import { openAiModelInfoSaneDefaults } from "@shared/api"
import { OpenAiModelsRequest } from "@shared/proto/cline/models"
import { getQaxAIPlatformBaseUrl } from "@shared/qax"
import { Mode } from "@shared/storage/types"
import { VSCodeCheckbox, VSCodeTextField } from "@vscode/webview-ui-toolkit/react"
import { useCallback, useEffect, useRef, useState } from "react"
import { Api<PERSON>eyField } from "@/components/settings/common/ApiKeyField"
import { BaseUrlField } from "@/components/settings/common/BaseUrlField"
import { DebouncedTextField } from "@/components/settings/common/DebouncedTextField"
import { ModelInfoView } from "@/components/settings/common/ModelInfoView"
import { getModeSpecificFields, normalizeApiConfiguration } from "@/components/settings/utils/providerUtils"
import { useApiConfigurationHandlers } from "@/components/settings/utils/useApiConfigurationHandlers"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { QaxUtilsServiceClient } from "@/services/grpc-client"
import { getAsVar, VSC_DESCRIPTION_FOREGROUND } from "@/utils/vscStyles"
import { useModelDropdown } from "../hooks/useModelDropdown"

/**
 * Props for the QaxProvider component
 */
interface QaxProviderProps {
	showModelOptions: boolean
	isPopup?: boolean
	currentMode: Mode
}

/**
 * The QAX provider configuration component
 */
export const QaxProvider = ({ showModelOptions, isPopup, currentMode }: QaxProviderProps) => {
	const { apiConfiguration } = useExtensionState()
	const { handleFieldChange, handleModeFieldChange } = useApiConfigurationHandlers()
	const modeFields = getModeSpecificFields(apiConfiguration, currentMode)

	const [modelConfigurationSelected, setModelConfigurationSelected] = useState(false)
	const [availableModels, setAvailableModels] = useState<string[]>([])
	const [isLoadingModels, setIsLoadingModels] = useState(false)

	// Get the normalized configuration
	const { selectedModelId, selectedModelInfo } = normalizeApiConfiguration(apiConfiguration, currentMode)

	// Get mode-specific fields
	const { qaxModelInfo } = modeFields

	// Use model dropdown hook
	const {
		searchTerm,
		isDropdownVisible,
		setIsDropdownVisible,
		selectedIndex,
		setSelectedIndex,
		dropdownRef,
		itemRefs,
		dropdownListRef,
		handleModelChange: onModelChange,
		handleKeyDown,
	} = useModelDropdown(availableModels, modeFields.qaxModelId || "", (newModelId: string) => {
		handleModeFieldChange({ plan: "planModeQaxModelId", act: "actModeQaxModelId" }, newModelId, currentMode)
	})

	// Debounced function to refresh QAX models (prevents excessive API calls while typing)
	const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)

	useEffect(() => {
		return () => {
			if (debounceTimerRef.current) {
				clearTimeout(debounceTimerRef.current)
			}
		}
	}, [])

	const debouncedRefreshQaxModels = useCallback((baseUrl?: string, apiKey?: string) => {
		if (debounceTimerRef.current) {
			clearTimeout(debounceTimerRef.current)
		}

		// Use default base URL if not provided
		const effectiveBaseUrl = baseUrl || getQaxAIPlatformBaseUrl()

		if (effectiveBaseUrl && apiKey) {
			debounceTimerRef.current = setTimeout(() => {
				setIsLoadingModels(true)

				QaxUtilsServiceClient.getQaxModels(
					OpenAiModelsRequest.create({
						baseUrl: effectiveBaseUrl,
						apiKey,
					}),
				)
					.then((response: any) => {
						setAvailableModels(response.values || [])
					})
					.catch((error: any) => {
						console.error("[QaxProvider] Failed to refresh QAX models:", error)
						setAvailableModels([])
					})
					.finally(() => {
						setIsLoadingModels(false)
					})
			}, 500)
		} else {
			setAvailableModels([])
		}
	}, [])

	// Load models on component mount if API key is available
	useEffect(() => {
		if (apiConfiguration?.qaxApiKey) {
			debouncedRefreshQaxModels(apiConfiguration.qaxBaseUrl || getQaxAIPlatformBaseUrl(), apiConfiguration.qaxApiKey)
		} else {
			setAvailableModels([])
		}
	}, [apiConfiguration?.qaxApiKey, apiConfiguration?.qaxBaseUrl, debouncedRefreshQaxModels])

	// Reset model configuration expanded state when mode changes
	useEffect(() => {
		setModelConfigurationSelected(false)
	}, [currentMode])

	return (
		<div>
			<ApiKeyField
				initialValue={apiConfiguration?.qaxApiKey || ""}
				onChange={(value) => {
					handleFieldChange("qaxApiKey", value)
					debouncedRefreshQaxModels(apiConfiguration?.qaxBaseUrl || getQaxAIPlatformBaseUrl(), value)
				}}
				providerName="QAX"
			/>

			<BaseUrlField
				initialValue={apiConfiguration?.qaxBaseUrl || ""}
				label="Base URL"
				onChange={(value) => {
					handleFieldChange("qaxBaseUrl", value)
					debouncedRefreshQaxModels(value || getQaxAIPlatformBaseUrl(), apiConfiguration?.qaxApiKey)
				}}
				placeholder={getQaxAIPlatformBaseUrl()}
			/>

			{/* Model Selection */}
			<div style={{ marginTop: 15 }}>
				<label htmlFor="qax-model">
					<span style={{ fontWeight: 500 }}>Model</span>
				</label>
				<div ref={dropdownRef} style={{ position: "relative", width: "100%" }}>
					<VSCodeTextField
						id="qax-model"
						onFocus={() => setIsDropdownVisible(true)}
						onKeyDown={handleKeyDown}
						placeholder={isLoadingModels ? "Loading models..." : "Click to select a model..."}
						readOnly={true}
						style={{ width: "100%", marginTop: "4px", position: "relative", zIndex: 1000 }}
						value={searchTerm}>
						<div
							aria-label="Open dropdown"
							className="input-icon-button codicon codicon-chevron-down"
							onClick={() => setIsDropdownVisible(!isDropdownVisible)}
							slot="end"
							style={{
								display: "flex",
								justifyContent: "center",
								alignItems: "center",
								height: "100%",
								cursor: "pointer",
							}}
						/>
					</VSCodeTextField>
					{isDropdownVisible && availableModels.length > 0 && (
						<div
							ref={dropdownListRef}
							style={{
								position: "absolute",
								top: "calc(100% - 3px)",
								left: 0,
								width: "calc(100% - 2px)",
								maxHeight: "200px",
								overflowY: "auto",
								backgroundColor: "var(--vscode-dropdown-background)",
								border: "1px solid var(--vscode-list-activeSelectionBackground)",
								borderBottomLeftRadius: "3px",
								borderBottomRightRadius: "3px",
								zIndex: 999,
							}}>
							{availableModels.length > 0 ? (
								availableModels.map((model, index) => {
									const isSelected = model === modeFields.qaxModelId
									const isHighlighted = index === selectedIndex
									return (
										<div
											key={model}
											onClick={() => {
												onModelChange(model)
											}}
											onMouseEnter={() => setSelectedIndex(index)}
											ref={(el) => (itemRefs.current[index] = el)}
											style={{
												padding: "5px 10px",
												cursor: "pointer",
												wordBreak: "break-all",
												whiteSpace: "normal",
												backgroundColor: isHighlighted
													? "var(--vscode-list-activeSelectionBackground)"
													: isSelected
														? "var(--vscode-list-inactiveSelectionBackground)"
														: "inherit",
												color: isSelected ? "var(--vscode-list-activeSelectionForeground)" : "inherit",
												fontWeight: isSelected ? "bold" : "normal",
											}}>
											{model}
										</div>
									)
								})
							) : (
								<div
									style={{
										padding: "10px",
										textAlign: "center",
										color: "var(--vscode-descriptionForeground)",
										fontStyle: "italic",
									}}>
									{isLoadingModels ? "Loading models..." : "No models available"}
								</div>
							)}
						</div>
					)}
				</div>
			</div>

			{/* Model Configuration Toggle */}
			<div
				onClick={() => setModelConfigurationSelected((val) => !val)}
				style={{
					color: getAsVar(VSC_DESCRIPTION_FOREGROUND),
					display: "flex",
					margin: "15px 0 10px 0",
					cursor: "pointer",
					alignItems: "center",
				}}>
				<span
					className={`codicon ${modelConfigurationSelected ? "codicon-chevron-down" : "codicon-chevron-right"}`}
					style={{ marginRight: "4px" }}
				/>
				<span style={{ fontWeight: 500, textTransform: "uppercase" }}>Model Configuration</span>
			</div>

			{modelConfigurationSelected && (
				<>
					<VSCodeCheckbox
						checked={!!qaxModelInfo?.supportsImages}
						onChange={(e: any) => {
							const isChecked = e.target.checked === true
							const modelInfo = qaxModelInfo ? qaxModelInfo : { ...openAiModelInfoSaneDefaults }
							modelInfo.supportsImages = isChecked
							handleModeFieldChange(
								{ plan: "planModeQaxModelInfo", act: "actModeQaxModelInfo" },
								modelInfo,
								currentMode,
							)
						}}>
						Supports Images
					</VSCodeCheckbox>

					<VSCodeCheckbox
						checked={!!qaxModelInfo?.isR1FormatRequired}
						onChange={(e: any) => {
							const isChecked = e.target.checked === true
							let modelInfo = qaxModelInfo ? qaxModelInfo : { ...openAiModelInfoSaneDefaults }
							modelInfo = { ...modelInfo, isR1FormatRequired: isChecked }
							handleModeFieldChange(
								{ plan: "planModeQaxModelInfo", act: "actModeQaxModelInfo" },
								modelInfo,
								currentMode,
							)
						}}>
						Enable R1 messages format
					</VSCodeCheckbox>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								qaxModelInfo?.contextWindow
									? qaxModelInfo.contextWindow.toString()
									: (openAiModelInfoSaneDefaults.contextWindow?.toString() ?? "")
							}
							onChange={(value) => {
								const modelInfo = qaxModelInfo ? qaxModelInfo : { ...openAiModelInfoSaneDefaults }
								modelInfo.contextWindow = Number(value)
								handleModeFieldChange(
									{ plan: "planModeQaxModelInfo", act: "actModeQaxModelInfo" },
									modelInfo,
									currentMode,
								)
							}}
							style={{ flex: 1 }}>
							<span style={{ fontWeight: 500 }}>Context Window Size</span>
						</DebouncedTextField>

						<DebouncedTextField
							initialValue={
								qaxModelInfo?.maxTokens
									? qaxModelInfo.maxTokens.toString()
									: (openAiModelInfoSaneDefaults.maxTokens?.toString() ?? "")
							}
							onChange={(value) => {
								const modelInfo = qaxModelInfo ? qaxModelInfo : { ...openAiModelInfoSaneDefaults }
								modelInfo.maxTokens = Number(value)
								handleModeFieldChange(
									{ plan: "planModeQaxModelInfo", act: "actModeQaxModelInfo" },
									modelInfo,
									currentMode,
								)
							}}
							style={{ flex: 1 }}>
							<span style={{ fontWeight: 500 }}>Max Output Tokens</span>
						</DebouncedTextField>
					</div>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								qaxModelInfo?.inputPrice
									? qaxModelInfo.inputPrice.toString()
									: (openAiModelInfoSaneDefaults.inputPrice?.toString() ?? "")
							}
							onChange={(value) => {
								const modelInfo = qaxModelInfo ? qaxModelInfo : { ...openAiModelInfoSaneDefaults }
								modelInfo.inputPrice = Number(value)
								handleModeFieldChange(
									{ plan: "planModeQaxModelInfo", act: "actModeQaxModelInfo" },
									modelInfo,
									currentMode,
								)
							}}
							style={{ flex: 1 }}>
							<span style={{ fontWeight: 500 }}>Input Price / 1M tokens</span>
						</DebouncedTextField>

						<DebouncedTextField
							initialValue={
								qaxModelInfo?.outputPrice
									? qaxModelInfo.outputPrice.toString()
									: (openAiModelInfoSaneDefaults.outputPrice?.toString() ?? "")
							}
							onChange={(value) => {
								const modelInfo = qaxModelInfo ? qaxModelInfo : { ...openAiModelInfoSaneDefaults }
								modelInfo.outputPrice = Number(value)
								handleModeFieldChange(
									{ plan: "planModeQaxModelInfo", act: "actModeQaxModelInfo" },
									modelInfo,
									currentMode,
								)
							}}
							style={{ flex: 1 }}>
							<span style={{ fontWeight: 500 }}>Output Price / 1M tokens</span>
						</DebouncedTextField>
					</div>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								qaxModelInfo?.temperature
									? qaxModelInfo.temperature.toString()
									: (openAiModelInfoSaneDefaults.temperature?.toString() ?? "")
							}
							onChange={(value) => {
								const modelInfo = qaxModelInfo ? qaxModelInfo : { ...openAiModelInfoSaneDefaults }

								const shouldPreserveFormat = value.endsWith(".") || (value.includes(".") && value.endsWith("0"))

								modelInfo.temperature =
									value === ""
										? openAiModelInfoSaneDefaults.temperature
										: shouldPreserveFormat
											? (value as any)
											: parseFloat(value)

								handleModeFieldChange(
									{ plan: "planModeQaxModelInfo", act: "actModeQaxModelInfo" },
									modelInfo,
									currentMode,
								)
							}}>
							<span style={{ fontWeight: 500 }}>Temperature</span>
						</DebouncedTextField>
					</div>
				</>
			)}

			<p
				style={{
					fontSize: "12px",
					marginTop: 10,
					color: "var(--vscode-descriptionForeground)",
				}}>
				QAX 模型经过优化，能够提供高质量的代码生成和问题解决能力。
			</p>

			{showModelOptions && (
				<ModelInfoView isPopup={isPopup} modelInfo={selectedModelInfo} selectedModelId={selectedModelId} />
			)}
		</div>
	)
}
