import { StringRequest } from "@shared/proto/cline/common"
import { UpdateTerminalConnectionTimeoutResponse } from "@shared/proto/index.cline"
import { VSCodeCheckbox, VSCodeDropdown, VSCodeOption, VSCodeTextField } from "@vscode/webview-ui-toolkit/react"
import React, { useState, useEffect } from "react"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { StateServiceClient } from "../../../services/grpc-client"
import Section from "../Section"
import TerminalOutputLineLimitSlider from "../TerminalOutputLineLimitSlider"
import { updateSetting } from "../utils/settingsHandlers"

interface TerminalSettingsSectionProps {
	renderSectionHeader: (tabId: string) => JSX.Element | null
}

export const TerminalSettingsSection: React.FC<TerminalSettingsSectionProps> = ({ renderSectionHeader }) => {
	const {
		shellIntegrationTimeout,
		terminalReuseEnabled,
		defaultTerminalProfile,
		availableTerminalProfiles,
		terminalDetectionMaxAttempts,
		terminalDetectionIdleTimeMs,
		terminalDetectionMaxOutputChars,
		terminalDetectionMaxLastLines
	} = useExtensionState()

	const [inputValue, setInputValue] = useState((shellIntegrationTimeout / 1000).toString())
	const [inputError, setInputError] = useState<string | null>(null)

	// 🔑 终端检测配置状态 - 从扩展状态初始化
	const [detectionMaxAttempts, setDetectionMaxAttempts] = useState(
		(terminalDetectionMaxAttempts ?? 10).toString()
	)
	const [detectionIdleTime, setDetectionIdleTime] = useState(
		((terminalDetectionIdleTimeMs ?? 10000) / 1000).toString()
	)
	const [detectionMaxOutput, setDetectionMaxOutput] = useState(
		((terminalDetectionMaxOutputChars ?? 65536) / 1024).toString()
	)
	const [detectionMaxLines, setDetectionMaxLines] = useState(
		(terminalDetectionMaxLastLines ?? 10).toString()
	)

	// 🔑 同步扩展状态到本地状态
	useEffect(() => {
		setDetectionMaxAttempts((terminalDetectionMaxAttempts ?? 10).toString())
	}, [terminalDetectionMaxAttempts])

	useEffect(() => {
		setDetectionIdleTime(((terminalDetectionIdleTimeMs ?? 10000) / 1000).toString())
	}, [terminalDetectionIdleTimeMs])

	useEffect(() => {
		setDetectionMaxOutput(((terminalDetectionMaxOutputChars ?? 65536) / 1024).toString())
	}, [terminalDetectionMaxOutputChars])

	useEffect(() => {
		setDetectionMaxLines((terminalDetectionMaxLastLines ?? 10).toString())
	}, [terminalDetectionMaxLastLines])

	const handleTimeoutChange = (event: Event) => {
		const target = event.target as HTMLInputElement
		const value = target.value

		setInputValue(value)

		const seconds = parseFloat(value)
		if (Number.isNaN(seconds) || seconds <= 0) {
			setInputError("Please enter a positive number")
			return
		}

		setInputError(null)
		const timeoutMs = Math.round(seconds * 1000)

		StateServiceClient.updateTerminalConnectionTimeout({ timeoutMs })
			.then((response: UpdateTerminalConnectionTimeoutResponse) => {
				const timeoutMs = response.timeoutMs
				// Backend calls postStateToWebview(), so state will update via subscription
				// Just sync the input value with the confirmed backend value
				if (timeoutMs !== undefined) {
					setInputValue((timeoutMs / 1000).toString())
				}
			})
			.catch((error) => {
				console.error("Failed to update terminal connection timeout:", error)
			})
	}

	const handleInputBlur = () => {
		if (inputError) {
			setInputValue((shellIntegrationTimeout / 1000).toString())
			setInputError(null)
		}
	}

	const handleTerminalReuseChange = (event: Event) => {
		const target = event.target as HTMLInputElement
		const checked = target.checked
		updateSetting("terminalReuseEnabled", checked)
	}

	// Use any to avoid type conflicts between Event and FormEvent
	const handleDefaultTerminalProfileChange = (event: any) => {
		const target = event.target as HTMLSelectElement
		const profileId = target.value

		// Save immediately - the backend will call postStateToWebview() to update our state
		StateServiceClient.updateDefaultTerminalProfile({
			value: profileId || "default",
		} as StringRequest).catch((error) => {
			console.error("Failed to update default terminal profile:", error)
		})
	}

	// 🔑 新增：终端检测配置事件处理
	const handleDetectionMaxAttemptsChange = (event: any) => {
		const input = event.target as HTMLInputElement
		// 只允许数字输入
		input.value = input.value.replace(/[^0-9]/g, "")
		const value = input.value
		setDetectionMaxAttempts(value)
		const numValue = parseInt(value, 10)
		if (!isNaN(numValue) && numValue >= 1 && numValue <= 20) {
			updateSetting("terminalDetectionMaxAttempts", numValue)
		}
	}

	const handleDetectionIdleTimeChange = (event: any) => {
		const input = event.target as HTMLInputElement
		// 只允许数字输入
		input.value = input.value.replace(/[^0-9]/g, "")
		const value = input.value
		setDetectionIdleTime(value)
		const numValue = parseInt(value, 10)
		if (!isNaN(numValue) && numValue >= 5 && numValue <= 60) {
			updateSetting("terminalDetectionIdleTimeMs", numValue * 1000) // 转换为毫秒
		}
	}

	const handleDetectionMaxOutputChange = (event: any) => {
		const input = event.target as HTMLInputElement
		// 只允许数字输入
		input.value = input.value.replace(/[^0-9]/g, "")
		const value = input.value
		setDetectionMaxOutput(value)
		const numValue = parseInt(value, 10)
		if (!isNaN(numValue) && numValue >= 16 && numValue <= 256) {
			updateSetting("terminalDetectionMaxOutputChars", numValue * 1024) // 转换为字节
		}
	}

	const handleDetectionMaxLinesChange = (event: any) => {
		const input = event.target as HTMLInputElement
		// 只允许数字输入
		input.value = input.value.replace(/[^0-9]/g, "")
		const value = input.value
		setDetectionMaxLines(value)
		const numValue = parseInt(value, 10)
		if (!isNaN(numValue) && numValue >= 3 && numValue <= 50) {
			updateSetting("terminalDetectionMaxLastLines", numValue)
		}
	}

	const profilesToShow = availableTerminalProfiles

	return (
		<div>
			{renderSectionHeader("terminal")}
			<Section>
				<div className="mb-5" id="terminal-settings-section">
					<div className="mb-4">
						<label className="font-medium block mb-1" htmlFor="default-terminal-profile">
							Default Terminal Profile
						</label>
						<VSCodeDropdown
							className="w-full"
							id="default-terminal-profile"
							onChange={handleDefaultTerminalProfileChange}
							value={defaultTerminalProfile || "default"}>
							{profilesToShow.map((profile) => (
								<VSCodeOption key={profile.id} title={profile.description} value={profile.id}>
									{profile.name}
								</VSCodeOption>
							))}
						</VSCodeDropdown>
						<p className="text-xs text-[var(--vscode-descriptionForeground)] mt-1">
							Select the default terminal Cline will use. 'Default' uses your VSCode global setting.
						</p>
					</div>

					<div className="mb-4">
						<div className="mb-2">
							<label className="font-medium block mb-1">Shell integration timeout (seconds)</label>
							<div className="flex items-center">
								<VSCodeTextField
									className="w-full"
									onBlur={handleInputBlur}
									onChange={(event) => handleTimeoutChange(event as Event)}
									placeholder="Enter timeout in seconds"
									value={inputValue}
								/>
							</div>
							{inputError && <div className="text-[var(--vscode-errorForeground)] text-xs mt-1">{inputError}</div>}
						</div>
						<p className="text-xs text-[var(--vscode-descriptionForeground)]">
							Set how long Cline waits for shell integration to activate before executing commands. Increase this
							value if you experience terminal connection timeouts.
						</p>
					</div>

					<div className="mb-4">
						<div className="flex items-center mb-2">
							<VSCodeCheckbox
								checked={terminalReuseEnabled ?? true}
								onChange={(event) => handleTerminalReuseChange(event as Event)}>
								Enable aggressive terminal reuse
							</VSCodeCheckbox>
						</div>
						<p className="text-xs text-[var(--vscode-descriptionForeground)]">
							When enabled, Cline will reuse existing terminal windows that aren't in the current working directory.
							Disable this if you experience issues with task lockout after a terminal command.
						</p>
					</div>
					<TerminalOutputLineLimitSlider />

					{/* 🔑 新增：终端检测配置 */}
					<div className="mt-6">
						<h3 className="font-medium text-sm mb-3 text-[var(--vscode-foreground)]">Terminal Detection Settings</h3>
						<p className="text-xs text-[var(--vscode-descriptionForeground)] mb-4">
							Configure how Cline detects and handles different types of terminal programs (services, interactive
							programs, etc.)
						</p>

						{/* 检测次数设置 */}
						<div className="mb-4">
							<label className="font-medium block mb-1 text-sm" htmlFor="detection-max-attempts">
								Maximum Detection Attempts
							</label>
							<VSCodeTextField
								className="w-full"
								id="detection-max-attempts"
								onInput={handleDetectionMaxAttemptsChange}
								placeholder="10"
								value={detectionMaxAttempts}
							/>
							<p className="text-xs text-[var(--vscode-descriptionForeground)] mt-1">
								Maximum number of attempts to detect program type before treating as normal command (1-20,
								default: 10)
							</p>
						</div>

						{/* 空闲时间设置 */}
						<div className="mb-4">
							<label className="font-medium block mb-1 text-sm" htmlFor="detection-idle-time">
								Detection Idle Time (seconds)
							</label>
							<VSCodeTextField
								className="w-full"
								id="detection-idle-time"
								onInput={handleDetectionIdleTimeChange}
								placeholder="10"
								value={detectionIdleTime}
							/>
							<p className="text-xs text-[var(--vscode-descriptionForeground)] mt-1">
								Time to wait (in seconds) after no output before attempting program detection (5-60, default: 10)
							</p>
						</div>

						{/* 输出缓冲区大小设置 */}
						<div className="mb-4">
							<label className="font-medium block mb-1 text-sm" htmlFor="detection-max-output">
								Output Buffer Size (KB)
							</label>
							<VSCodeTextField
								className="w-full"
								id="detection-max-output"
								onInput={handleDetectionMaxOutputChange}
								placeholder="64"
								value={detectionMaxOutput}
							/>
							<p className="text-xs text-[var(--vscode-descriptionForeground)] mt-1">
								Maximum size of terminal output buffer in KB (16-256, default: 64)
							</p>
						</div>

						{/* 检测行数设置 */}
						<div className="mb-4">
							<label className="font-medium block mb-1 text-sm" htmlFor="detection-max-lines">
								Detection Analysis Lines
							</label>
							<VSCodeTextField
								className="w-full"
								id="detection-max-lines"
								onInput={handleDetectionMaxLinesChange}
								placeholder="10"
								value={detectionMaxLines}
							/>
							<p className="text-xs text-[var(--vscode-descriptionForeground)] mt-1">
								Number of last lines to analyze for program type detection (3-50, default: 10)
							</p>
						</div>
					</div>

					<div className="mt-5 p-3 bg-[var(--vscode-textBlockQuote-background)] rounded border border-[var(--vscode-textBlockQuote-border)]">
						<p className="text-[13px] m-0">
							<strong>Having terminal issues?</strong> Check our{" "}
							<a
								className="text-[var(--vscode-textLink-foreground)] underline hover:no-underline"
								href="https://docs.cline.bot/troubleshooting/terminal-quick-fixes"
								rel="noopener noreferrer"
								target="_blank">
								Terminal Quick Fixes
							</a>{" "}
							or the{" "}
							<a
								className="text-[var(--vscode-textLink-foreground)] underline hover:no-underline"
								href="https://docs.cline.bot/troubleshooting/terminal-integration-guide"
								rel="noopener noreferrer"
								target="_blank">
								Complete Troubleshooting Guide
							</a>
							.
						</p>
					</div>
				</div>
			</Section>
		</div>
	)
}

export default TerminalSettingsSection
