# v3.24.0 到 v3.25.2 升级日志

## 基本信息
- **更新日期**: 2025-08-15
- **源版本**: v3.24.0
- **目标版本**: v3.25.2
- **变更统计**: 101 个文件变更，新增 4,059 行，删除 664 行

## 变更统计

### 目标版本已删除或重命名文件列表
#### 重命名的文件 (4 个)
- **docs/provider-config/aws-bedrock-with-apikey-authentication.mdx** → **docs/provider-config/aws-bedrock/api-key.mdx** - AWS Bedrock API 密钥认证文档重组
- **docs/provider-config/aws-bedrock-with-profile-authentication.mdx** → **docs/provider-config/aws-bedrock/cli-profile.mdx** - AWS Bedrock CLI 配置文档重组
- **docs/provider-config/aws-bedrock-with-credentials-authentication.mdx** → **docs/provider-config/aws-bedrock/iam-credentials.mdx** - AWS Bedrock IAM 凭证文档重组
- **src/test/e2e/utils/build.js** → **src/test/e2e/utils/build.mjs** - 测试构建脚本模块化

#### 删除的文件 (1 个)
- **src/test/e2e/utils/global.teardown.ts** - 全局测试清理脚本（功能整合到其他模块）

### 关键文件 `package.json` 变更内容
```json
{
  "name": "claude-dev",
  "displayName": "Cline",
  "description": "Autonomous coding agent right in your IDE, capable of creating/editing files, running commands, using the browser, and more with your permission every step of the way.",
- "version": "3.24.0",
+ "version": "3.25.2",
  // 版本从 3.24.0 直接升级到 3.25.2，包含 v3.25.0、v3.25.1 和 v3.25.2 的所有改进
}
```

## 主要变更

### 🚀 重大新功能

#### 1. Focus Chain & Deep Planning 功能 (#5409)
- **核心功能**:
  - 新增 `src/core/task/focus-chain/index.ts` (514 行) - 焦点链核心逻辑
  - 新增 `src/core/task/focus-chain/file-utils.ts` (81 行) - 文件工具函数
  - 新增 `src/core/task/focus-chain/utils.ts` (27 行) - 焦点链工具函数
  - 新增 `docs/features/focus-chain.mdx` (296 行) - 功能文档
  - 新增 `docs/features/slash-commands/deep-planning.mdx` (153 行) - 深度规划文档
- **功能特性**:
  - 智能任务分解和执行链
  - 深度规划模式，支持复杂任务的自动分解
  - 焦点文件管理，提高代码编辑效率
  - 默认启用焦点链功能
- **UI 集成**:
  - 新增 `webview-ui/src/components/chat/task-header/TaskHeader.tsx` (240 行) - 任务头部组件
  - 新增 `webview-ui/src/components/common/ChecklistRenderer.tsx` (142 行) - 检查清单渲染器
  - 改进任务时间线显示和交互

#### 2. 自动上下文压缩功能 (#5520)
- **智能压缩**:
  - 新增 `src/core/prompts/contextManagement.ts` (75 行) - 上下文管理提示
  - 改进 `src/core/context/context-management/ContextManager.ts` (76 行变更) - 上下文管理器
  - 新增 `docs/features/auto-compact.mdx` (55 行) - 自动压缩功能文档
- **性能优化**:
  - 自动检测和压缩冗余上下文
  - 智能保留关键信息
  - 减少令牌使用，提高响应速度

#### 3. 新增 VSCode 命令集成
- **右键菜单命令**:
  - 新增 `src/core/controller/commands/addToCline.ts` (34 行) - "添加到 Cline" 命令
  - 新增 `src/core/controller/commands/explainWithCline.ts` (23 行) - "用 Cline 解释" 命令
  - 新增 `src/core/controller/commands/fixWithCline.ts` (20 行) - "用 Cline 修复" 命令
  - 新增 `src/core/controller/commands/improveWithCline.ts` (25 行) - "用 Cline 改进" 命令
- **协议支持**:
  - 新增 `proto/cline/commands.proto` (29 行) - 命令协议定义
  - 新增 `src/core/prompts/commands.ts` (185 行) - 命令提示系统
- **外部主机支持**:
  - 允许外部主机触发 "Add to Cline" 操作
  - 改进命令工具函数和集成

### 🔧 模型和提供商增强

#### 1. AWS Bedrock 提供商大幅增强
- **新增模型支持**:
  - 新增 GPT-OSS 模型支持 (#5412)
  - 扩展 `src/api/providers/bedrock.ts` (141 行新增代码)
  - 改进模型配置和错误处理
- **文档重组**:
  - AWS Bedrock 文档重新组织到专门目录
  - 改进认证方式说明和配置指南

#### 2. Claude Sonnet 4 变体切换功能 (#5585)
- **动态模型切换**:
  - 新增 Anthropic 提供商的模型变体切换功能
  - 新增 `webview-ui/src/components/settings/providers/AnthropicProvider.tsx` (41 行)
  - 支持 200k 上下文窗口的 Claude Sonnet 4 (通过 OpenRouter/Cline)
- **模型优化**:
  - 改进 `src/core/prompts/model_prompts/claude4.ts` (255 行变更)
  - 优化模型提示和响应处理

#### 3. OpenRouter 集成改进
- **错误处理**:
  - 修复 OpenRouter 在 402 响应后显示 Cline 积分错误的问题 (#5590)
  - 改进 `src/api/transform/openrouter-stream.ts` (13 行变更)
  - 优化模型刷新和错误恢复机制
- **模型管理**:
  - 改进 `webview-ui/src/components/settings/OpenRouterModelPicker.tsx` (40 行变更)
  - 更好的模型选择和配置体验

#### 4. Requesty 提供商支持 (#5579)
- **新增提供商**:
  - 改进 `src/api/providers/requesty.ts` (3 行变更)
  - 新增 `webview-ui/src/components/settings/providers/RequestyProvider.tsx` (27 行)
  - 完整的配置界面和文档支持

### 🛠️ 系统架构和性能优化

#### 1. 控制器系统简化
- **代码精简**:
  - 简化 `src/core/controller/index.ts` (109 行减少)
  - 简化 `src/extension.ts` (145 行减少)
  - 移除冗余逻辑，提高启动性能
- **功能增强**:
  - 新增 `src/core/controller/file/openFocusChainFile.ts` (40 行) - 焦点链文件操作
  - 改进 `src/core/controller/ui/initializeWebview.ts` (10 行变更) - WebView 初始化

#### 2. 任务执行系统增强
- **执行器优化**:
  - 扩展 `src/core/task/ToolExecutor.ts` (145 行新增)
  - 改进 `src/core/task/index.ts` (151 行新增)
  - 新增 `src/core/task/TaskState.ts` (9 行变更) - 任务状态管理
- **错误处理**:
  - 改进任务执行的错误恢复机制
  - 更好的任务状态跟踪和报告

#### 3. 存储和状态管理
- **缓存服务**:
  - 改进 `src/core/storage/CacheService.ts` (17 行变更)
  - 修复缓存服务未正确填充所有预期状态的问题 (#5586)
- **状态键管理**:
  - 扩展 `src/core/storage/state-keys.ts` (7 行新增)
  - 新增 `src/shared/FocusChainSettings.ts` (11 行) - 焦点链设置

### 🎨 用户界面和体验改进

#### 1. 聊天界面重大改进
- **任务显示**:
  - 修复 attempt_completion 命令在聊天视图中显示两次的问题 (#5588, #5583)
  - 改进任务进度检查清单更新逻辑
  - 新增 `webview-ui/src/components/chat/ChatRow.tsx` (77 行) - 聊天行组件
- **按钮状态**:
  - 修复询问问题工具显示 "开始新任务" 按钮的问题 (#5581)
  - 改进 `webview-ui/src/components/chat/chat-view/shared/buttonConfig.ts` (6 行变更)
  - 修复导航栏中新任务按钮的问题 (#5577)

#### 2. 设置界面优化
- **功能设置**:
  - 扩展 `webview-ui/src/components/settings/sections/FeatureSettingsSection.tsx` (47 行变更)
  - 新增焦点链和自动压缩功能的设置选项
  - 改进设置界面的布局和交互

#### 3. 公告和通知系统
- **公告组件**:
  - 改进 `webview-ui/src/components/chat/Announcement.tsx` (39 行变更)
  - 更好的用户通知和信息展示
  - 支持重要功能更新的公告

### 🧪 测试和开发体验

#### 1. 诊断系统增强
- **诊断工具**:
  - 新增 `src/hosts/vscode/hostbridge/workspace/getDiagnostics.test.ts` (197 行) - 诊断测试
  - 改进 `src/hosts/vscode/hostbridge/workspace/getDiagnostics.ts` (95 行变更)
  - 新增 `src/integrations/diagnostics/__tests__/index.test.ts` (50 行) - 集成测试
- **工具函数**:
  - 新增诊断工具函数和实用程序
  - 改进错误检测和报告机制

#### 2. 测试基础设施改进
- **Playwright 配置**:
  - 改进 `playwright.config.ts` (11 行变更) - 配置 Playwright 在失败时保留视频
  - 简化测试清理流程
  - 删除 `src/test/e2e/utils/global.teardown.ts`，功能整合
- **测试工具**:
  - 改进 `src/test/e2e/utils/common.ts` (31 行变更) - 通用测试工具
  - 更新多个 E2E 测试文件，提高测试可靠性

#### 3. 遥测和监控
- **遥测服务**:
  - 大幅扩展 `src/services/posthog/telemetry/TelemetryService.ts` (170 行新增)
  - 新增 `src/services/posthog/feature-flags/FeatureFlagsService.ts` (19 行) - 功能标志服务
  - 改进主机桥遥测和启动监控 (#5561)
- **机器标识**:
  - 新增 `src/hosts/vscode/hostbridge/env/getMachineId.ts` (7 行) - 机器 ID 获取
  - 改进用户和应用状态一致性检查 (#5549)

### 📚 文档和配置

#### 1. 文档系统更新
- **新增功能文档**:
  - 新增焦点链功能完整文档
  - 新增深度规划斜杠命令文档
  - 新增自动压缩功能说明
- **提供商文档重组**:
  - AWS Bedrock 文档重新组织
  - 改进配置指南和示例

#### 2. 协议和 API 更新
- **协议定义**:
  - 扩展多个 proto 文件，支持新功能
  - 改进 `src/shared/api.ts` (26 行变更) - API 接口
  - 新增命令协议和状态管理协议

## 详细文件列表

### 新增文件 (15 个)
1. **docs/features/auto-compact.mdx** - 自动压缩功能文档
2. **docs/features/focus-chain.mdx** - 焦点链功能文档
3. **docs/features/slash-commands/deep-planning.mdx** - 深度规划文档
4. **proto/cline/commands.proto** - 命令协议定义
5. **src/core/controller/commands/addToCline.ts** - 添加到 Cline 命令
6. **src/core/controller/commands/explainWithCline.ts** - 解释命令
7. **src/core/controller/commands/fixWithCline.ts** - 修复命令
8. **src/core/controller/commands/improveWithCline.ts** - 改进命令
9. **src/core/controller/file/openFocusChainFile.ts** - 焦点链文件操作
10. **src/core/prompts/commands.ts** - 命令提示系统
11. **src/core/prompts/contextManagement.ts** - 上下文管理提示
12. **src/core/task/focus-chain/file-utils.ts** - 焦点链文件工具
13. **src/core/task/focus-chain/index.ts** - 焦点链核心逻辑
14. **src/core/task/focus-chain/utils.ts** - 焦点链工具函数
15. **src/shared/FocusChainSettings.ts** - 焦点链设置定义

### 修改文件 (81 个)

#### 核心系统文件 (8 个)
1. **CHANGELOG.md** - 添加 v3.25.0-v3.25.2 版本说明
2. **package.json** - 版本从 3.24.0 更新到 3.25.2
3. **package-lock.json** - 依赖版本同步更新
4. **.vscodeignore** - 忽略文件配置更新
5. **esbuild.mjs** - 构建配置优化
6. **docs/docs.json** - 文档索引更新
7. **scripts/runclinecore.sh** - 核心运行脚本更新
8. **playwright.config.ts** - 测试配置改进

#### API 和提供商 (5 个)
9. **src/api/index.ts** - API 索引更新
10. **src/api/providers/bedrock.ts** - Bedrock 提供商大幅增强
11. **src/api/providers/requesty.ts** - Requesty 提供商改进
12. **src/api/transform/openrouter-stream.ts** - OpenRouter 流处理修复
13. **src/common.ts** - 通用工具函数更新

#### 核心控制器和任务系统 (15 个)
14. **src/core/controller/index.ts** - 控制器核心简化
15. **src/core/controller/models/refreshOpenRouterModels.ts** - 模型刷新改进
16. **src/core/controller/state/updateSettings.ts** - 设置更新逻辑
17. **src/core/controller/ui/initializeWebview.ts** - WebView 初始化
18. **src/core/task/TaskState.ts** - 任务状态管理
19. **src/core/task/ToolExecutor.ts** - 工具执行器增强
20. **src/core/task/index.ts** - 任务核心逻辑扩展
21. **src/core/assistant-message/index.ts** - 助手消息处理
22. **src/core/mentions/index.ts** - 提及功能优化
23. **src/core/prompts/model_prompts/claude4.ts** - Claude 4 提示优化
24. **src/core/prompts/system.ts** - 系统提示增强
25. **src/core/slash-commands/index.ts** - 斜杠命令更新
26. **src/core/context/context-management/ContextManager.ts** - 上下文管理器重构
27. **src/core/storage/CacheService.ts** - 缓存服务修复
28. **src/core/storage/state-keys.ts** - 状态键扩展

#### 主机和集成系统 (12 个)
29. **src/extension.ts** - 扩展主文件简化
30. **src/hosts/vscode/commandUtils.ts** - VSCode 命令工具
31. **src/hosts/vscode/hostbridge-grpc-handler.ts** - gRPC 处理器
32. **src/hosts/vscode/hostbridge/client/host-grpc-client-base.ts** - gRPC 客户端基类
33. **src/hosts/vscode/hostbridge/env/getMachineId.ts** - 机器 ID 获取
34. **src/hosts/vscode/hostbridge/workspace/getDiagnostics.ts** - 诊断获取
35. **src/integrations/diagnostics/index.ts** - 诊断集成
36. **src/services/error/ClineError.ts** - 错误服务
37. **src/services/posthog/PostHogClientProvider.ts** - PostHog 客户端
38. **src/services/posthog/feature-flags/FeatureFlagsService.ts** - 功能标志服务
39. **src/services/posthog/telemetry/TelemetryService.ts** - 遥测服务大幅扩展
40. **src/standalone/cline-core.ts** - 独立核心

#### 共享组件和协议 (8 个)
41. **src/shared/ExtensionMessage.ts** - 扩展消息协议
42. **src/shared/api.ts** - API 接口更新
43. **src/shared/proto-conversions/cline-message.ts** - 消息转换
44. **src/shared/proto-conversions/models/api-configuration-conversion.ts** - API 配置转换
45. **src/shared/proto-conversions/state/settings-conversion.ts** - 设置转换
46. **src/standalone/protobus-service.ts** - 协议总线服务
47. **proto/cline/file.proto** - 文件协议
48. **proto/cline/models.proto** - 模型协议

#### WebView UI 组件 (15 个)
49. **webview-ui/src/components/chat/Announcement.tsx** - 公告组件
50. **webview-ui/src/components/chat/ChatRow.tsx** - 聊天行组件
51. **webview-ui/src/components/chat/ChatView.tsx** - 聊天视图
52. **webview-ui/src/components/chat/ErrorRow.tsx** - 错误行组件
53. **webview-ui/src/components/chat/chat-view/components/layout/TaskSection.tsx** - 任务区域
54. **webview-ui/src/components/chat/chat-view/shared/buttonConfig.ts** - 按钮配置
55. **webview-ui/src/components/chat/chat-view/utils/messageUtils.ts** - 消息工具
56. **webview-ui/src/components/chat/task-header/TaskHeader.tsx** - 任务头部组件
57. **webview-ui/src/components/chat/task-header/TaskTimeline.tsx** - 任务时间线
58. **webview-ui/src/components/common/ChecklistRenderer.tsx** - 检查清单渲染器
59. **webview-ui/src/components/menu/Navbar.tsx** - 导航栏
60. **webview-ui/src/components/settings/OpenRouterModelPicker.tsx** - OpenRouter 模型选择器
61. **webview-ui/src/components/settings/providers/AnthropicProvider.tsx** - Anthropic 提供商设置
62. **webview-ui/src/components/settings/providers/RequestyProvider.tsx** - Requesty 提供商设置
63. **webview-ui/src/components/settings/sections/FeatureSettingsSection.tsx** - 功能设置区域

#### 测试和工具文件 (18 个)
64. **src/test/e2e/chat.test.ts** - 聊天 E2E 测试
65. **src/test/e2e/diff.test.ts** - 差异 E2E 测试
66. **src/test/e2e/editor.test.ts** - 编辑器 E2E 测试
67. **src/test/e2e/fixtures/server/index.ts** - 测试服务器
68. **src/test/e2e/utils/common.ts** - 通用测试工具
69. **src/test/e2e/utils/global.setup.ts** - 全局测试设置
70. **src/test/e2e/utils/helpers.ts** - 测试辅助函数
71. **src/hosts/vscode/hostbridge/workspace/getDiagnostics.test.ts** - 诊断测试
72. **src/integrations/diagnostics/__tests__/index.test.ts** - 诊断集成测试
73. **webview-ui/src/context/ExtensionStateContext.tsx** - 扩展状态上下文
74. **webview-ui/src/utils/slash-commands.ts** - 斜杠命令工具
75. **webview-ui/src/utils/validate.ts** - 验证工具
76. **docs/features/drag-and-drop.mdx** - 拖拽功能文档
77. **docs/features/understanding-context-management.mdx** - 上下文管理文档
78. **proto/cline/state.proto** - 状态协议
79. **proto/cline/ui.proto** - UI 协议
80. **proto/host/env.proto** - 环境协议
81. **src/core/storage/utils/state-helpers.ts** - 状态辅助函数

### 重点关注文件变更
1. **package.json** (6 行变更) - 版本从 3.24.0 更新到 3.25.2
2. **src/core/controller/index.ts** (109 行减少) - 控制器核心简化，移除冗余逻辑
3. **src/core/webview/index.ts** - 无直接变更，架构保持稳定
4. **src/extension.ts** (145 行减少) - 扩展主文件简化，提高启动性能
5. **src/shared/ExtensionMessage.ts** (7 行新增) - 新增消息类型支持
6. **src/shared/WebviewMessage.ts** - 无直接变更，WebView 消息保持稳定
7. **webview-ui/src/context/ExtensionStateContext.tsx** (4 行变更) - 状态上下文优化

## 升级注意事项

### ✅ 兼容性说明
- **完全向后兼容**: 保持完全的向后兼容性，现有配置和数据不受影响
- **API 稳定性**: 核心 API 保持稳定，新增功能不影响现有集成
- **配置自动迁移**: 新功能设置自动初始化，无需手动配置
- **扩展兼容**: 与现有 VSCode 扩展和工作流程完全兼容

### 🔧 新功能亮点
1. **Focus Chain & Deep Planning**:
   - 革命性的任务分解和执行系统
   - 智能焦点文件管理，提高编码效率
   - 深度规划模式，处理复杂任务
   - 默认启用，立即可用
2. **自动上下文压缩**:
   - 智能压缩冗余上下文，节省令牌
   - 保留关键信息，提高响应质量
   - 自动优化性能，无需手动干预
3. **VSCode 命令集成**:
   - 右键菜单直接调用 Cline 功能
   - 支持解释、修复、改进代码
   - 外部主机集成支持
4. **模型和提供商增强**:
   - Claude Sonnet 4 变体切换
   - AWS Bedrock GPT-OSS 支持
   - OpenRouter 错误处理改进
   - Requesty 提供商完整支持

### 📋 建议操作
1. **立即升级**: 强烈建议升级以获得重大功能改进和性能优化
2. **体验新功能**:
   - 尝试焦点链功能，体验智能任务分解
   - 使用深度规划模式处理复杂项目
   - 测试右键菜单的新命令
   - 体验自动上下文压缩的性能提升
3. **配置优化**:
   - 检查功能设置中的新选项
   - 验证模型提供商的改进配置
   - 体验改进的用户界面

### ⚠️ 重要提醒
- **焦点链功能**: 默认启用，可在设置中调整
- **上下文管理**: 自动压缩功能可能改变上下文处理方式
- **命令集成**: 新的右键菜单命令需要重新加载 VSCode 窗口
- **测试配置**: Playwright 配置更新，CI/CD 流程可能需要调整
- **遥测增强**: 新增遥测功能，可在设置中控制

### 🔍 技术细节

#### 1. Focus Chain 架构
- **智能分解**: 自动将复杂任务分解为可管理的子任务
- **焦点管理**: 智能选择和管理相关文件，减少上下文切换
- **执行链**: 按逻辑顺序执行任务，确保一致性
- **状态跟踪**: 完整的任务状态管理和进度跟踪

#### 2. 上下文压缩技术
- **智能分析**: 自动识别冗余和重复内容
- **关键保留**: 保留对任务执行关键的信息
- **动态压缩**: 根据上下文大小动态调整压缩策略
- **性能优化**: 显著减少令牌使用，提高响应速度

#### 3. 命令系统集成
- **协议扩展**: 新的命令协议支持复杂操作
- **外部集成**: 支持外部主机和工具的集成
- **用户体验**: 无缝的右键菜单集成
- **可扩展性**: 易于添加新命令和功能

#### 4. 模型提供商优化
- **动态切换**: 支持运行时模型变体切换
- **错误恢复**: 改进的错误处理和恢复机制
- **配置简化**: 更直观的提供商配置界面
- **性能监控**: 增强的遥测和性能跟踪

---

**发布说明**: v3.25.2 是一个里程碑版本，引入了革命性的 Focus Chain 功能和深度规划能力，同时大幅改进了用户体验和系统性能。这个版本代表了 Cline 在智能代码助手领域的重大进步。

**技术支持**: 如遇到升级相关问题，请参考相关文档或提交 Issue。特别注意新的焦点链功能和上下文管理系统的改进。

**贡献者**: 感谢所有为此版本贡献代码、测试和反馈的开发者和用户！特别感谢对 Focus Chain 功能、深度规划系统和用户体验改进的贡献。
