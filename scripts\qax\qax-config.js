/**
 * Qax Codegen 构建配置文件
 * 集中管理 Qax Codegen 扩展的所有配置信息
 * <AUTHOR>
 */

const path = require("path")
const fs = require("fs")

// 获取项目根目录
const ROOT_DIR = path.resolve(__dirname, "../..")
const QAX_DIR = path.resolve(__dirname, ".")

// 环境配置 - 简化版，只保留域名配置
const ENVIRONMENTS = {
	dev: {
		name: "development",
		qaxDomain: "https://codegen-dev.qianxin-inc.cn",
		aiDomain: "https://aip.b.qianxin-inc.cn",
	},
	test: {
		name: "testing",
		qaxDomain: "https://codegen-test.qianxin-inc.cn",
		aiDomain: "https://aip.b.qianxin-inc.cn",
	},
	prod: {
		name: "production",
		qaxDomain: "https://codegen.qianxin-inc.cn",
		aiDomain: "https://aip.b.qianxin-inc.cn",
	},
}

// 获取当前环境配置
function getEnvironmentConfig(env = "prod") {
	return ENVIRONMENTS[env] || ENVIRONMENTS.prod
}

// 文本替换规则 - 包含所有用户可见的文案替换
const TEXT_REPLACEMENTS = {
	// === 现有的核心替换 ===
	// 扩展标识符替换（核心）
	'"saoudrizwan.claude-dev"': '"qi-anxin-group.qax-codegen"',
	"'saoudrizwan.claude-dev'": "'qi-anxin-group.qax-codegen'",

	// 命令名称替换（核心）
	'registerCommand("cline.': 'registerCommand("qax-codegen.',
	'executeCommand("cline.': 'executeCommand("qax-codegen.',
	'command: "cline.': 'command: "qax-codegen.',
	'"setContext", "cline.': '"setContext", "qax-codegen.',

	// 视图和容器ID替换（核心）
	'"claude-dev.': '"qax-codegen.',
	"claude-dev-ActivityBar": "qax-codegen-ActivityBar",

	// Walkthrough ID 替换（核心）
	"saoudrizwan.claude-dev#ClineWalkthrough": "qi-anxin-group.qax-codegen#QaxCodegenWalkthrough",

	// Output Channel 名称替换（核心）
	'createOutputChannel("Cline")': 'createOutputChannel("Qax Codegen")',
	'name: "Cline"': 'name: "Qax Codegen"',

	// 标题栏和窗口标题替换（核心）
	'createWebviewPanel(VscodeWebviewProvider.TAB_PANEL_ID, "Cline"':
		'createWebviewPanel(VscodeWebviewProvider.TAB_PANEL_ID, "Codegen"',
	"<title>Cline</title>": "<title>Codegen</title>",

	// 日志消息替换（核心）
	'"Cline extension activated"': '"Qax Codegen extension activated"',
	'"ClineProvider instantiated"': '"Qax Codegen Provider instantiated"',

	// === 新增的用户可见文案替换 ===

	// 工具执行提示 (ToolExecutor.ts) - 高优先级
	"Cline wants to edit": "Codegen wants to edit",
	"Cline wants to create": "Codegen wants to create",
	"Cline wants to read": "Codegen wants to read",
	"Cline wants to view directory": "Codegen wants to view directory",
	"Cline wants to view source code definitions": "Codegen wants to view source code definitions",
	"Cline wants to search files": "Codegen wants to search files",
	"Cline wants to use a browser and launch": "Codegen wants to use a browser and launch",
	"Cline wants to execute a command:": "Codegen wants to execute a command:",
	"Cline wants to use": "Codegen wants to use",
	"Cline wants to access": "Codegen wants to access",
	"Cline wants to start a new task...": "Codegen wants to start a new task...",
	"Cline wants to condense the conversation...": "Codegen wants to condense the conversation...",
	"Cline wants to create a github issue...": "Codegen wants to create a github issue...",
	"Cline wants to fetch content from": "Codegen wants to fetch content from",

	// 系统通知消息 - 高优先级
	"Cline is suggesting to start a new task with:": "Codegen is suggesting to start a new task with:",
	"Cline is suggesting to condense your conversation with:": "Codegen is suggesting to condense your conversation with:",
	"Cline is suggesting to create a github issue with the title:":
		"Codegen is suggesting to create a github issue with the title:",
	"Cline is having trouble. Would you like to continue the task?":
		"Codegen is having trouble. Would you like to continue the task?",

	// WebView UI 文案 - 高优先级
	"Hi, I'm Cline": "Hi, I'm Codegen",
	'Quick <span className="text-white">[Wins]</span> with Cline':
		'Quick <span className="text-white">[Wins]</span> with Codegen',
	"Cline is having trouble...": "Codegen is having trouble...",
	"Cline wants to execute this command:": "Codegen wants to execute this command:",
	"Cline has a question:": "Codegen has a question:",
	"Cline wants to edit this file:": "Codegen wants to edit this file:",
	"Cline wants to create a new file:": "Codegen wants to create a new file:",
	"Cline wants to read this file": "Codegen wants to read this file",
	"Cline wants to view the top level files": "Codegen wants to view the top level files",
	"Cline wants to recursively view all files": "Codegen wants to recursively view all files",
	"Cline wants to view source code definition names": "Codegen wants to view source code definition names",
	"Cline wants to search this directory": "Codegen wants to search this directory",
	"Cline is condensing the conversation:": "Codegen is condensing the conversation:",
	"Cline wants to fetch content from this URL:": "Codegen wants to fetch content from this URL:",
	"Cline wants to start a new task:": "Codegen wants to start a new task:",
	"Cline wants to condense your conversation:": "Codegen wants to condense your conversation:",
	"Cline wants to create a Github issue:": "Codegen wants to create a Github issue:",
	"Cline is using the browser:": "Codegen is using the browser:",
	"Cline wants to use the browser:": "Codegen wants to use the browser:",
	"Sign in to Cline": "Sign in to Codegen",
	"Cline tried to access": "Codegen tried to access",

	// 设置界面文案 - 中优先级
	"Cline works best with": "Codegen works best with",
	"Cline uses complex prompts and works best with Claude models":
		"Codegen uses complex prompts and works best with Claude models",
	"Help improve Cline by sending usage data": "Help improve Codegen by sending usage data",
	"Select the default terminal Cline will use": "Select the default terminal Codegen will use",
	"Set how long Cline waits for shell integration": "Set how long Codegen waits for shell integration",
	"When enabled, Cline will reuse existing terminal windows": "When enabled, Codegen will reuse existing terminal windows",
	"Prevent Cline from using browser actions": "Prevent Codegen from using browser actions",
	"Enable Cline to use your Chrome": "Enable Codegen to use your Chrome",
	"The language that Cline should use for communication": "The language that Codegen should use for communication",

	// 自动批准文案 - 中优先级
	"Auto-approve allows Cline to perform": "Auto-approve allows Codegen to perform",
	"Cline will automatically make this many API requests": "Codegen will automatically make this many API requests",
	"Allows Cline to read files": "Allows Codegen to read files",
	"Allows Cline to modify files": "Allows Codegen to modify files",
	"Allows Cline to execute safe terminal commands": "Allows Codegen to execute safe terminal commands",
	"Allows Cline to execute all terminal commands": "Allows Codegen to execute all terminal commands",
	"Allows Cline to launch and interact with any website": "Allows Codegen to launch and interact with any website",
	"Allows Cline to use configured MCP servers": "Allows Codegen to use configured MCP servers",
	"Receive system notifications when Cline requires approval": "Receive system notifications when Codegen requires approval",

	// 其他重要文案 - 低优先级
	"Available in the Cline provider for free": "Available in the Codegen provider for free",
	"This significantly helps Cline stay on track": "This significantly helps Codegen stay on track",
	"transforms Cline into an architect": "transforms Codegen into an architect",
	"Cline/OpenRouter users get instant access": "Codegen/OpenRouter users get instant access",
	"Cline is now optimized to work with": "Codegen is now optimized to work with",
	"Cline's capabilities": "Codegen's capabilities",
	"or ask Cline to create new tools": "or ask Codegen to create new tools",
	"Help Improve Cline": "Help Improve Codegen",
	"Cline collects error and usage data": "Codegen collects error and usage data",
	"Create a new Cline rule based on your conversation": "Create a new Codegen rule based on your conversation",
	"Create a Github issue with Cline": "Create a Github issue with Codegen",
	"Cline Version": "Codegen Version",
	"Cline may have trouble viewing": "Codegen may have trouble viewing",
	"Interval (in messages) to remind Cline about": "Interval (in messages) to remind Codegen about",
	"Sign Up with Cline": "Sign Up with Codegen",

	// === VS Code 代码操作（小灯泡菜单）中的文本替换 - 高优先级 ===
	// 这些文本在 src/extension.ts 的 CodeActionProvider 中硬编码为字符串字面量
	'"Add to Cline"': '"Add to Codegen"',
	'"Explain with Cline"': '"Explain with Codegen"',
	'"Improve with Cline"': '"Improve with Codegen"',
	'"Fix with Cline"': '"Fix with Codegen"',

	// 注意：命令名称的替换已经通过上面的通用规则处理：
	// 'registerCommand("cline.' -> 'registerCommand("qax-codegen.'
	// 'executeCommand("cline.' -> 'executeCommand("qax-codegen.'
	// 'command: "cline.' -> 'command: "qax-codegen.'
}

// 需要临时复制的文件列表
const TEMP_COPY_FILES = [
	{
		from: "scripts/qax/assets/icons/icon.png",
		to: "assets/icons/icon.png",
	},
	{
		from: "scripts/qax/assets/icons/icon.svg",
		to: "assets/icons/icon.svg",
	},
	{
		from: "scripts/qax/assets/icons/robot_panel_light.png",
		to: "assets/icons/robot_panel_light.png",
	},
	{
		from: "scripts/qax/assets/icons/robot_panel_dark.png",
		to: "assets/icons/robot_panel_dark.png",
	},
	{
		from: "webview-ui/src/qax/assets/ClineLogoVariable.tsx",
		to: "webview-ui/src/assets/ClineLogoVariable.tsx",
	},
	{
		from: "scripts/qax/docs/README.md",
		to: "README.md",
	},
	{
		from: "scripts/qax/docs/CHANGELOG.md",
		to: "CHANGELOG.md",
	},
	// QAX 专用组件替换 - 构建时临时替换，构建后自动恢复
	{
		from: "webview-ui/src/components/settings/QaxApiOptions.tsx",
		to: "webview-ui/src/components/settings/ApiOptions.tsx",
	},
	// 确保codicon文件被正确复制到扩展包中
	// {
	// 	from: "node_modules/@vscode/codicons/dist/codicon.css",
	// 	to: "node_modules/@vscode/codicons/dist/codicon.css",
	// },
	// {
	// 	from: "node_modules/@vscode/codicons/dist/codicon.ttf",
	// 	to: "node_modules/@vscode/codicons/dist/codicon.ttf",
	// },
]

// 需要应用文本替换的文件模式（这些文件会被临时修改，构建后自动恢复）
const TEXT_REPLACEMENT_PATTERNS = {
	// === 核心扩展文件 - 打包必需 ===
	//- 包含命令注册和扩展激活日志的文件
	"src/extension.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	//- 包含视图ID的文件
	"src/core/webview/index.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"src/core/webview/WebviewProvider.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"src/hosts/vscode/VscodeWebviewProvider.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	//- 包含命令调用和 Provider 实例化日志的文件
	"src/core/controller/index.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	// - 包含扩展引用的文件
	"src/core/task/ToolExecutor.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	// - 包含任务相关文案的文件
	"src/core/task/index.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	// - 包含扩展标识符的文件
	"src/standalone/vscode-context.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	// - 包含终端名称的文件
	"src/integrations/terminal/TerminalRegistry.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	// - 包含主题扩展引用的文件
	"src/integrations/theme/getTheme.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	// - 包含命令执行调用的工具文件
	"src/hosts/vscode/commandUtils.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},

	// === WebView UI 核心组件 - 用户界面文案 ===
	"webview-ui/src/components/welcome/WelcomeView.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/welcome/SuggestedTasks.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/chat/ChatRow.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/chat/BrowserSessionRow.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/chat/ErrorRow.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/chat/Announcement.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/chat/ReportBugPreview.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/chat/ChatTextArea.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},

	// === 设置相关组件 - 配置界面文案 ===
	"webview-ui/src/components/settings/sections/TerminalSettingsSection.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/sections/GeneralSettingsSection.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/sections/BrowserSettingsSection.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/sections/FeatureSettingsSection.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/PreferredLanguageSetting.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/BasetenModelPicker.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/SapAiCoreModelPicker.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/OpenRouterModelPicker.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/RequestyModelPicker.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/GroqModelPicker.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/ClineAccountInfoCard.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},

	// === 提供商组件 - 模型配置文案 ===
	"webview-ui/src/components/settings/providers/TogetherProvider.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/providers/NebiusProvider.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/providers/XaiProvider.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/providers/LMStudioProvider.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/providers/OllamaProvider.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/providers/ClineProvider.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/settings/providers/OpenAICompatible.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},

	// === 自动批准相关 - 权限管理文案 ===
	"webview-ui/src/components/chat/auto-approve-menu/AutoApproveModal.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/chat/auto-approve-menu/constants.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},

	// === 其他重要组件 - 辅助功能文案 ===
	"webview-ui/src/components/common/TelemetryBanner.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/components/mcp/configuration/tabs/installed/InstalledServersView.tsx": {
		textReplacements: TEXT_REPLACEMENTS,
	},
	"webview-ui/src/utils/slash-commands.ts": {
		textReplacements: TEXT_REPLACEMENTS,
	},

	// === 其他可能需要的文件（可选） ===
	//- 开发和工具文件（可选，用于开发测试）
	// "src/dev/commands/tasks.ts": {
	// 	textReplacements: TEXT_REPLACEMENTS,
	// },
	//- 评估系统文件（可选，用于开发测试）
	// "evals/cli/src/utils/vscode.ts": {
	// 	textReplacements: TEXT_REPLACEMENTS,
	// },
}

// 环境变量配置
function getEnvironmentVariables(env = "prod") {
	const envConfig = getEnvironmentConfig(env)

	return {
		// QAX 基础配置
		QAX_ENVIRONMENT: "true",
		QAX_CODEGEN_DOMAIN: envConfig.qaxDomain,
		QAX_AI_DOMAIN: envConfig.aiDomain,
		QAX_AUTOCOMPLETE: "true",

		// 扩展标识符
		VSCODE_EXTENSION_ID: "qi-anxin-group.qax-codegen",
	}
}

/**
 * 生成构建脚本配置 - 简化版
 * 所有环境都使用相同的打包流程，通过环境变量区分配置
 * @returns {Object} 构建脚本配置
 */
function getBuildScripts() {
	return {
		// 预构建脚本 - 不需要预构建，直接打包
		prebuild: [],

		// 构建脚本 - 所有环境都使用完整的 package 命令进行打包
		build: ["npm run package"],

		// 后构建脚本 - 暂时为空
		postbuild: [],
	}
}

// 默认的 BUILD_SCRIPTS
const BUILD_SCRIPTS = getBuildScripts()

// 构建配置 - 简化版
const BUILD_CONFIG = {
	buildTimeout: 300000, // 5分钟
	maxBackupCount: 3,
	buildSteps: {
		package: "完整打包：类型检查 + 前端构建 + 代码检查 + 主构建 + VSIX 生成（一体化流程）",
	},
}

/**
 * 合并 package.json 配置 - 简化版
 * @param {string} originalPackageJsonPath - 原始 package.json 路径
 * @param {string} qaxPackageJsonPath - Qax package.json 路径
 * @returns {Object} 合并后的 package.json 对象
 */
function mergePackageJson(originalPackageJsonPath, qaxPackageJsonPath) {
	const originalPackage = JSON.parse(fs.readFileSync(originalPackageJsonPath, "utf8"))
	const qaxPackage = JSON.parse(fs.readFileSync(qaxPackageJsonPath, "utf8"))

	// Qax 配置覆盖原始配置
	const mergedPackage = { ...originalPackage, ...qaxPackage }

	console.log(`✅ 已合并 package.json 配置`)
	console.log(`   - 扩展标识符: ${mergedPackage.name}`)
	console.log(`   - 显示名称: ${mergedPackage.displayName}`)
	console.log(`   - 版本号: ${mergedPackage.version}`)

	return mergedPackage
}

// 路径配置
const PATHS = {
	root: ROOT_DIR,
	qax: QAX_DIR,
	originalPackageJson: path.join(ROOT_DIR, "package.json"),
	qaxPackageJson: path.join(QAX_DIR, "package.json"),
	dist: path.join(ROOT_DIR, "dist"),
	webviewBuild: path.join(ROOT_DIR, "webview-ui/build"),
	clineBackup: path.join(ROOT_DIR, "qax/cline-backup"),
}

// 导出配置
module.exports = {
	ROOT_DIR,
	QAX_DIR,
	ENVIRONMENTS,
	TEXT_REPLACEMENTS,
	TEMP_COPY_FILES,
	TEXT_REPLACEMENT_PATTERNS,
	BUILD_SCRIPTS,
	BUILD_CONFIG,
	PATHS,
	getEnvironmentConfig,
	getEnvironmentVariables,
	getBuildScripts,
	mergePackageJson,
}
