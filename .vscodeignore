# Default
.vscode/**
.vscode-test/**
out/
dist-standalone/
node_modules/
src/**
standalone/**
.gitignore
.yarnrc
esbuild.js
vsc-extension-quickstart.md
tsconfig*.json
**/.eslintrc.json
**/*.map
**/*.ts
**/.vscode-test.*
eslint-rules/**
.github/**
.husky/**

# Custom
**/demo.gif
.nvmrc
.gitattributes
.prettierignore
.husky/
.github/
eslint-rules/
old_docs/
evals/
.changie.yaml
.codespellrc
.mocharc.json
buf.yaml
.changeset/
.clinerules/

# Ignore all webview-ui files except the build directory (https://github.com/microsoft/vscode-webview-ui-toolkit-samples/blob/main/frameworks/hello-world-react-cra/.vscodeignore)
webview-ui/src/**
webview-ui/public/**
webview-ui/index.html
webview-ui/README.md
webview-ui/package.json
webview-ui/package-lock.json
webview-ui/node_modules/**
**/.gitignore

# Ignore docs
docs/**
old_docs/**

# Fix issue where codicons don't get packaged (https://github.com/microsoft/vscode-extension-samples/issues/692)
!node_modules/@vscode/codicons/dist/codicon.css
!node_modules/@vscode/codicons/dist/codicon.ttf

# Include default themes JSON files used in getTheme
!src/integrations/theme/default-themes/**

# Include icons
!assets/icons/**

# Ignore E2E build files
e2e-build.mjs
e2e.vsix
test-results/

# Ignore QAX build directory
qax/
scripts/qax
scripts/build-qax.js
.mocharc.autocomplete.json
.mocharc.providers.json
.mocharc.test.json
test-autocomplete-prompt-debugger.js
