# v3.25.2 到 v3.26.0 升级日志

## 基本信息
- **更新日期**: 2025-08-20
- **源版本**: v3.25.2
- **目标版本**: v3.26.0
- **变更统计**: 554 个文件变更，新增 6,002 行，删除 10,171 行

## 变更统计

### 目标版本已删除或重命名文件列表

#### 删除的文件 (11 个)
- **.eslintrc.json** - ESLint 配置文件（迁移到 Biome）
- **.prettierignore** - Prettier 忽略文件（迁移到 Biome）
- **.prettierrc.json** - Prettier 配置文件（迁移到 Biome）
- **eslint-rules/__tests__/no-direct-vscode-api.test.ts** - 自定义 ESLint 规则测试
- **eslint-rules/__tests__/no-direct-vscode-state-api.test.ts** - 自定义 ESLint 规则测试
- **eslint-rules/index.js** - ESLint 规则入口文件
- **eslint-rules/no-direct-vscode-api.js** - VSCode API 直接访问规则
- **eslint-rules/no-direct-vscode-state-api.js** - VSCode 状态 API 直接访问规则
- **eslint-rules/package-lock.json** - ESLint 规则包锁定文件
- **eslint-rules/package.json** - ESLint 规则包配置
- **eslint-rules/tsconfig.json** - ESLint 规则 TypeScript 配置
- **src/utils/validation.ts** - 验证工具函数（功能整合到其他模块）
- **webview-ui/.eslintrc.json** - WebView UI ESLint 配置（迁移到 Biome）

#### 重命名/移动的文件 (48 个)
**API 模块重组 - 从 `src/api/` 移动到 `src/core/api/`**:
- **src/api/index.ts** → **src/core/api/index.ts** - API 主入口文件
- **src/api/providers/*.ts** → **src/core/api/providers/*.ts** - 所有 API 提供商文件
- **src/api/transform/*.ts** → **src/core/api/transform/*.ts** - 所有 API 转换文件

**系统提示重构**:
- **src/core/prompts/model_prompts/claude4.ts** → **src/core/prompts/system-prompt/families/next-gen-models/next-gen-system-prompt.ts** - Claude 4 提示重构
- **src/core/prompts/system.ts** → **src/core/prompts/system-prompt/generic-system-prompt.ts** - 通用系统提示重构

### 关键文件 `package.json` 变更内容
```json
{
  "name": "claude-dev",
  "displayName": "Cline",
  "description": "Autonomous coding agent right in your IDE, capable of creating/editing files, running commands, using the browser, and more with your permission every step of the way.",
- "version": "3.25.2",
+ "version": "3.26.0",
  // 开发依赖更新：移除 ESLint/Prettier，新增 Biome
- "@typescript-eslint/eslint-plugin": "^8.15.0",
- "@typescript-eslint/parser": "^8.15.0",
- "eslint": "^9.16.0",
- "prettier": "^3.4.2",
+ "@biomejs/biome": "1.9.4",
+ "knip": "^5.37.1",
}
```

## 主要变更

### 🚀 重大架构改进

#### 1. 代码质量工具链现代化 (#5423)
- **Biome 迁移**:
  - 完全替换 ESLint 和 Prettier，使用 Biome 进行代码检查和格式化
  - 新增 `biome.jsonc` (158 行) - Biome 配置文件
  - 删除所有 ESLint 相关配置和自定义规则
  - 性能提升：Biome 比 ESLint + Prettier 快 10-100 倍
- **代码清理**:
  - 应用 Biome 规则：noUnusedVariables, noUnusedFunctionParameters, noUnusedImports (#5545)
  - 移除未使用的源文件 (#5642)
  - 新增 `knip.json` (13 行) - 死代码检测配置

#### 2. API 模块重组 (#5539)
- **结构优化**:
  - 将整个 `src/api/` 目录移动到 `src/core/api/`
  - 统一核心模块的组织结构
  - 改进模块导入路径和依赖关系
- **影响范围**:
  - 48 个 API 相关文件重新组织
  - 更新所有相关导入引用
  - 保持 API 功能完全不变

#### 3. 系统提示架构重构
- **模块化设计**:
  - 新增 `src/core/prompts/system-prompt/build-system-prompt.ts` (24 行) - 系统提示构建器
  - 新增 `src/core/prompts/system-prompt/utils.ts` (32 行) - 系统提示工具函数
  - 新增 `src/core/prompts/system-prompt/user-instructions/addUserInstructions.ts` (41 行) - 用户指令集成
- **提示优化**:
  - 重构 Claude 4 和通用系统提示
  - 改进提示生成逻辑和模块化
  - 更好的用户指令集成机制

### 🔧 模型和提供商增强

#### 1. 新增 Z AI 提供商支持 (#5316)
- **新增提供商**:
  - 新增 `src/core/api/providers/zai.ts` (111 行) - Z AI GLM-4.5 和 GLM-4.5 air 模型支持
  - 新增 `webview-ui/src/components/settings/providers/ZAiProvider.tsx` (95 行) - Z AI 配置界面
  - 支持 GLM-4.5 和 GLM-4.5 air 模型
- **功能特性**:
  - 完整的 API 集成和错误处理
  - 用户友好的配置界面
  - 模型选择和参数配置

#### 2. LM Studio 本地模型增强 (#5591)
- **v0 API 端点支持**:
  - 改进 `src/core/api/providers/lmstudio.ts` (23 行变更)
  - 支持 LM Studio v0 API 端点的本地模型
  - 更好的本地模型发现和连接
- **配置改进**:
  - 改进 `webview-ui/src/components/settings/providers/LMStudioProvider.tsx` (180 行变更)
  - 更直观的本地模型配置界面
  - 自动检测和连接功能

#### 3. Cline/Sonic Stealth 模型支持 (#5669)
- **新模型集成**:
  - 新增 Cline/Sonic stealth 模型支持
  - 改进模型选择和配置选项
  - 优化模型性能和响应质量

### 🛠️ 主机桥和集成系统增强

#### 1. 新增主机桥 RPC 功能
- **活动编辑器信息**:
  - 新增 `src/hosts/vscode/hostbridge/window/getActiveEditor.ts` (8 行) - 获取活动编辑器信息
  - 改进编辑器状态跟踪和集成
- **问题面板集成**:
  - 新增 `src/hosts/vscode/hostbridge/workspace/openProblemsPanel.ts` (7 行) - 打开 IDE 问题面板
  - 改进诊断信息显示和用户体验
- **主机版本获取**:
  - 新增 `src/hosts/vscode/hostbridge/env/getHostVersion.ts` (7 行) - 获取扩展版本信息
  - 从 ExtensionContext 获取版本，而非硬编码

#### 2. VSCode 类型定义增强
- **类型安全**:
  - 新增 `src/hosts/vscode.d.ts` (240 行) - VSCode API 类型定义
  - 改进类型安全和开发体验
  - 更好的 IDE 支持和自动补全

#### 3. 协议和通信改进
- **协议扩展**:
  - 扩展 `proto/host/window.proto` (58 行变更) - 窗口操作协议
  - 扩展 `proto/host/workspace.proto` (8 行变更) - 工作区操作协议
  - 扩展 `proto/host/env.proto` (9 行变更) - 环境信息协议
- **测试基础设施**:
  - 新增 `scripts/test-hostbridge-server.ts` (139 行) - 主机桥测试服务器
  - 改进测试和开发工具

### 🎨 用户界面和体验改进

#### 1. 聊天界面优化
- **消息处理**:
  - 改进 `webview-ui/src/components/chat/ChatRow.tsx` (285 行变更)
  - 优化消息显示和交互逻辑
  - 更好的用户消息处理
- **文本区域增强**:
  - 改进 `webview-ui/src/components/chat/ChatTextArea.tsx` (132 行变更)
  - 更好的输入体验和功能
  - 改进自动完成和快捷操作

#### 2. 公告系统重构
- **公告组件**:
  - 大幅改进 `webview-ui/src/components/chat/Announcement.tsx` (93 行变更)
  - 更好的公告显示和用户交互
  - 支持富文本和链接

#### 3. 设置界面优化
- **提供商配置**:
  - 改进多个提供商配置组件
  - 更直观的 API 配置界面
  - 统一的用户体验设计

### 🧪 开发和测试改进

#### 1. 构建和开发工具
- **构建优化**:
  - 改进多个构建脚本和配置
  - 更快的构建和开发体验
  - 自动格式化集成 (#5675)
- **开发工具**:
  - 新增多个 Grit 模式文件用于代码重构
  - 改进开发工作流程和自动化

#### 2. 测试基础设施
- **配置更新**:
  - 改进 `.github/workflows/test.yml` (12 行变更) - CI/CD 配置
  - 更新 `.mocharc.json` (15 行变更) - Mocha 测试配置
  - 优化测试运行和报告

#### 3. 代码质量监控
- **静态分析**:
  - 集成 Knip 进行死代码检测
  - 改进代码质量监控和报告
  - 自动化代码清理流程

### 📚 功能和配置改进

#### 1. Focus Chain 功能优化
- **功能标志行为变更** (#5656):
  - 改进 Focus Chain 功能标志的行为逻辑
  - 更好的功能开关和用户控制
  - 优化默认设置和用户体验
- **工具函数共享**:
  - 新增 `src/shared/focus-chain-utils.ts` (43 行) - Focus Chain 共享工具函数
  - 改进代码复用和维护性

#### 2. 上下文管理增强
- **上下文截断**:
  - 使用标准上下文截断变更 (#5633)
  - 改进 `src/core/context/context-management/ContextManager.ts` (126 行变更)
  - 更智能的上下文管理和压缩
- **用户消息处理**:
  - 截断首个用户消息 (#5668)
  - 用户消息覆写功能 (#5614)
  - 更好的消息处理和显示

#### 3. MCP 设置监控迁移 (#5499)
- **Chokidar 迁移**:
  - 将 MCP 设置监控迁移到 chokidar
  - 改进文件监控性能和可靠性
  - 更好的跨平台兼容性

#### 4. 设置持久化修复 (#5632)
- **设置保存**:
  - 修复 'Enable Checkpoints' 和 'Disable MCP Marketplace' 设置在重新加载时重置的问题
  - 改进设置持久化机制
  - 确保用户配置的正确保存和恢复

### 🔍 错误处理和日志改进

#### 1. 错误处理优化
- **ClineError 处理** (#5617):
  - 修复 request_id 提取在 ClineError 处理中的问题
  - 改进错误跟踪和调试信息
  - 更好的错误报告和用户反馈
- **日志分类** (#5531):
  - 防止非错误日志被错误分类为错误
  - 改进日志级别和分类
  - 更准确的错误监控和报告

#### 2. 构建和部署改进
- **Lint-staged 修复** (#5673):
  - 防止 lint-staged 在错误时回滚已暂存的更改
  - 改进代码提交工作流程
  - 更可靠的代码质量检查
- **自动格式化** (#5675):
  - 在 postprotos 脚本中启用自动格式化以防止构建失败
  - 改进构建流程的可靠性
  - 自动化代码格式化

### 🚀 性能和稳定性提升

#### 1. 代码清理和优化
- **未使用代码移除**:
  - 移除未使用的源文件和函数
  - 应用 Biome 规则清理代码
  - 减少包大小和提高性能
- **依赖优化**:
  - 大幅减少 package-lock.json 大小 (1492 行减少)
  - 移除不必要的依赖
  - 优化构建和运行时性能

#### 2. 架构稳定性
- **模块重组**:
  - API 模块重组提高代码组织性
  - 更清晰的模块边界和依赖关系
  - 改进可维护性和扩展性
- **类型安全**:
  - 增强 TypeScript 类型定义
  - 更好的编译时错误检查
  - 提高代码质量和可靠性

## 详细文件列表

### 新增文件 (8 个)
1. **biome.jsonc** - Biome 代码质量工具配置
2. **knip.json** - 死代码检测配置
3. **scripts/test-hostbridge-server.ts** - 主机桥测试服务器
4. **src/core/api/providers/zai.ts** - Z AI 提供商支持
5. **src/core/prompts/system-prompt/build-system-prompt.ts** - 系统提示构建器
6. **src/core/prompts/system-prompt/utils.ts** - 系统提示工具函数
7. **src/core/prompts/system-prompt/user-instructions/addUserInstructions.ts** - 用户指令集成
8. **src/shared/focus-chain-utils.ts** - Focus Chain 共享工具函数

### 主要修改文件类别

#### 核心系统文件 (10 个)
1. **package.json** - 版本更新和依赖变更
2. **package-lock.json** - 依赖锁定文件大幅精简
3. **CHANGELOG.md** - 版本更新日志
4. **tsconfig.json** - TypeScript 配置优化
5. **esbuild.mjs** - 构建配置调整
6. **.vscode/settings.json** - VSCode 工作区设置
7. **.github/workflows/test.yml** - CI/CD 配置更新
8. **.husky/pre-commit** - Git 钩子配置
9. **.mocharc.json** - 测试配置更新
10. **docs/docs.json** - 文档索引更新

#### API 和提供商系统 (48 个文件重组)
- **完整 API 模块迁移**: 从 `src/api/` 到 `src/core/api/`
- **所有提供商文件**: anthropic, bedrock, openai, ollama, gemini 等
- **API 转换模块**: 格式转换和流处理
- **新增 Z AI 提供商**: 完整的 GLM-4.5 模型支持

#### 核心功能模块 (25 个)
11. **src/core/context/context-management/ContextManager.ts** - 上下文管理器重构
12. **src/core/task/ToolExecutor.ts** - 工具执行器优化
13. **src/core/task/index.ts** - 任务核心逻辑改进
14. **src/core/controller/index.ts** - 控制器核心优化
15. **src/extension.ts** - 扩展主文件改进
16. **src/core/storage/CacheService.ts** - 缓存服务优化
17. **src/core/mentions/index.ts** - 提及功能改进
18. **src/core/slash-commands/index.ts** - 斜杠命令更新
19. **src/shared/ExtensionMessage.ts** - 扩展消息协议
20. **src/shared/api.ts** - API 接口更新

#### WebView UI 组件 (50+ 个)
- **聊天界面**: ChatRow, ChatTextArea, Announcement 等大幅改进
- **设置界面**: 所有提供商配置组件优化
- **通用组件**: MarkdownBlock, ChecklistRenderer 等改进
- **新增 Z AI 配置**: ZAiProvider.tsx 完整实现

### 重点关注文件变更
1. **package.json** (20 行变更) - 版本从 3.25.2 更新到 3.26.0，依赖大幅简化
2. **src/core/controller/index.ts** (18 行变更) - 控制器核心逻辑优化
3. **src/core/webview/index.ts** - WebView 提供商架构保持稳定
4. **src/extension.ts** (7 行变更) - 扩展主文件精简和优化
5. **src/shared/ExtensionMessage.ts** (9 行变更) - 消息协议扩展
6. **src/shared/WebviewMessage.ts** - WebView 消息协议保持稳定
7. **webview-ui/src/context/ExtensionStateContext.tsx** (18 行变更) - 状态上下文优化

## 升级注意事项

### ✅ 兼容性说明
- **完全向后兼容**: 保持完全的向后兼容性，现有配置和数据不受影响
- **API 稳定性**: 核心 API 保持稳定，API 模块重组不影响外部接口
- **配置自动迁移**: 代码质量工具迁移自动完成，无需手动配置
- **扩展兼容**: 与现有 VSCode 扩展和工作流程完全兼容

### 🔧 新功能亮点
1. **代码质量工具现代化**:
   - Biome 替代 ESLint + Prettier，性能提升 10-100 倍
   - 统一的代码格式化和检查体验
   - 更快的开发和构建流程
2. **Z AI 模型支持**:
   - 新增 GLM-4.5 和 GLM-4.5 air 模型
   - 完整的配置界面和 API 集成
   - 扩展模型选择范围
3. **架构优化**:
   - API 模块重组提高代码组织性
   - 系统提示架构重构
   - 更好的模块化和可维护性
4. **主机桥功能增强**:
   - 新增活动编辑器信息获取
   - 问题面板集成
   - 更好的 IDE 集成体验

### 📋 建议操作
1. **立即升级**: 强烈建议升级以获得性能提升和代码质量改进
2. **体验新功能**:
   - 体验更快的代码格式化和检查
   - 尝试新的 Z AI 模型
   - 测试改进的主机桥功能
3. **开发环境**:
   - 如果有自定义构建脚本，检查 Biome 兼容性
   - 验证代码质量工具的新配置
   - 测试改进的开发工作流程

### ⚠️ 重要提醒
- **代码质量工具**: 已从 ESLint/Prettier 迁移到 Biome，自动应用新的格式化规则
- **API 模块路径**: 内部 API 模块路径已更改，但不影响外部使用
- **设置持久化**: 修复了设置重置问题，确保配置正确保存
- **构建流程**: 构建和开发流程已优化，可能需要重新安装依赖

### 🔍 技术细节

#### 1. Biome 迁移优势
- **性能提升**: 比 ESLint + Prettier 快 10-100 倍
- **统一工具**: 单一工具处理格式化和检查
- **更好配置**: 更简洁的配置文件和规则
- **现代化**: 使用 Rust 构建，更快更可靠

#### 2. API 模块重组影响
- **更好组织**: 所有 API 相关代码统一在 core 目录下
- **清晰边界**: 核心功能和 API 功能分离更清晰
- **易于维护**: 模块化结构便于维护和扩展
- **向后兼容**: 外部接口保持不变

#### 3. 系统提示重构
- **模块化设计**: 提示生成更加模块化和可配置
- **用户指令**: 更好的用户指令集成机制
- **性能优化**: 提示生成更高效
- **可扩展性**: 易于添加新的提示类型和功能

---

**发布说明**: v3.26.0 是一个重要的技术债务清理和架构优化版本，引入了现代化的代码质量工具链，重组了 API 模块结构，并新增了 Z AI 模型支持。这个版本显著提升了开发体验和代码质量。

**技术支持**: 如遇到升级相关问题，请参考相关文档或提交 Issue。特别注意新的 Biome 工具链和 API 模块重组。

**贡献者**: 感谢所有为此版本贡献代码、测试和反馈的开发者和用户！特别感谢对代码质量工具现代化、API 架构重组和新模型集成的贡献。
