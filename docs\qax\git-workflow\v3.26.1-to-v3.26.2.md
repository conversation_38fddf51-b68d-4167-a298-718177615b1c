# v3.26.1 到 v3.26.2 升级日志

## 基本信息
- **更新日期**: 2025-08-22
- **源版本**: v3.26.1
- **目标版本**: v3.26.2
- **变更统计**: 19 个文件变更，新增 699 行，删除 35 行

## 变更统计

### 目标版本已删除或重命名文件列表
本次升级没有删除或重命名任何文件。

### 关键文件 `package.json` 变更内容
```json
{
  "name": "claude-dev",
  "displayName": "Cline",
  "description": "Autonomous coding agent right in your IDE, capable of creating/editing files, running commands, using the browser, and more with your permission every step of the way.",
- "version": "3.26.1",
+ "version": "3.26.2",
}
```

## 主要变更

### 🧪 新增场景测试框架 (#5711)

#### 1. GitHub Actions 工作流
- **新增文件**: `.github/workflows/scenario.yml` (140 行)
  - 支持多平台测试 (Ubuntu, Windows, macOS)
  - 自动化场景验证和测试执行
  - 支持 PR 和主分支的自动测试
  - 智能缓存机制提高构建效率

#### 2. Playwright 场景测试配置
- **新增文件**: `playwright.scenarios.config.ts` (35 行)
  - Playwright 测试配置专用于场景测试
  - 支持多浏览器和设备测试
  - 配置测试超时和重试策略

#### 3. 场景验证脚本
- **新增文件**: `scripts/validate-scenario.sh` (151 行)
  - PR 特定场景元数据验证
  - 自动化场景测试流程
  - 测试结果验证和报告

#### 4. 场景测试文档和示例
- **新增文件**: `src/test/scenarios/README.md` (147 行)
  - 场景测试框架使用指南
  - 测试编写规范和最佳实践
  - 示例场景和测试用例
- **新增文件**: `src/test/scenarios/example.ts` (21 行)
  - 场景测试示例代码
  - 演示如何编写和执行场景测试

### 🔧 API 和模型改进

#### 1. OpenRouter 模型信息解析优化 (#5737, #5738)
- **改进文件**: `src/core/controller/models/refreshOpenRouterModels.ts` (70 行变更)
  - 改进 OpenRouter 模型信息解析逻辑
  - 修复模型上下文窗口大小报告不准确的问题
  - 特别修复 GPT-5 上下文窗口从错误的 400k 调整为正确的 272k
- **改进文件**: `src/core/api/transform/openrouter-stream.ts` (6 行变更)
  - 优化 OpenRouter 流处理
  - 改进错误处理和响应解析

#### 2. Sonic 模型配置优化 (#5742)
- **功能移除**: 从 Sonic 模型中移除 max tokens 限制
  - 允许更灵活的令牌使用
  - 改进模型响应质量和长度

#### 3. 错误处理增强 (#5741)
- **改进文件**: `src/core/context/context-management/context-error-handling.ts` (6 行变更)
  - 从错误消息中提取错误代码
  - 更精确的错误分类和处理
  - 改进用户错误反馈体验

### 📚 文档更新

#### 1. Vercel AI Gateway 提供商文档 (#5743)
- **更新文件**: `docs/provider-config/vercel-ai-gateway.mdx` (104 行变更)
  - 完善 Vercel AI Gateway 配置指南
  - 新增使用示例和最佳实践
  - 改进故障排除和常见问题解答

### 🐛 Bug 修复

#### 1. Focus Chain 设置页面修复 (#5744)
- **修复文件**: `webview-ui/src/components/settings/sections/FeatureSettingsSection.tsx` (2 行变更)
  - 修复 Focus Chain 设置页面的小错误
  - 改进用户界面显示和交互

#### 2. 深度规划提示支持 Go 文件 (#5640)
- **改进文件**: `src/core/prompts/commands.ts` (8 行变更)
  - 在深度规划提示中新增对 `*.go` 文件的支持
  - 扩展支持的文件类型范围
  - 改进 Go 项目的规划和分析能力

### 📊 遥测功能增强

#### 1. 菜单压缩切换遥测 (#5718)
- **改进文件**: `src/services/posthog/telemetry/TelemetryService.ts` (19 行变更)
  - 新增菜单中压缩切换功能的遥测跟踪
  - 帮助了解用户对压缩功能的使用情况
  - 为功能优化提供数据支持

### 🔧 开发和构建改进

#### 1. 预发布期间 CLI 忽略配置 (#5717)
- **改进文件**: `.gitignore` (3 行变更)
  - 在预发布期间将 `/cli` 添加到 gitignore
  - 避免开发过程中的临时文件干扰
  - 保持代码库的整洁

#### 2. 任务系统小幅改进
- **改进文件**: `src/core/task/index.ts` (3 行变更)
  - 任务处理逻辑的小幅优化
  - 改进任务执行的稳定性

#### 3. UI 组件微调
- **改进文件**: `webview-ui/src/components/settings/OpenRouterModelPicker.tsx` (1 行变更)
  - OpenRouter 模型选择器的小幅改进
  - 优化用户体验和界面显示

### 🧹 代码清理

#### 1. API 接口清理
- **清理文件**: `src/shared/api.ts` (1 行删除)
  - 移除未使用的 API 接口定义
  - 保持代码库的整洁和高效

## 详细文件列表

### 新增文件 (5 个)
1. **`.github/workflows/scenario.yml`** - GitHub Actions 场景测试工作流
2. **`playwright.scenarios.config.ts`** - Playwright 场景测试配置
3. **`scripts/validate-scenario.sh`** - 场景验证脚本
4. **`src/test/scenarios/README.md`** - 场景测试文档
5. **`src/test/scenarios/example.ts`** - 场景测试示例

### 重点关注文件变更
1. **package.json** (4 行变更) - 版本从 3.26.1 更新到 3.26.2
2. **src/core/controller/index.ts** - 核心控制器保持稳定
3. **src/core/webview/index.ts** - WebView 架构保持稳定  
4. **src/extension.ts** - 扩展主文件保持稳定
5. **src/shared/ExtensionMessage.ts** - 扩展消息协议保持稳定
6. **src/shared/WebviewMessage.ts** - WebView 消息协议保持稳定
7. **webview-ui/src/context/ExtensionStateContext.tsx** - 扩展状态上下文保持稳定

### 修改文件 (14 个)
1. **`.gitignore`** (3 行变更) - 新增 CLI 忽略配置
2. **`CHANGELOG.md`** (9 行变更) - 更新变更日志
3. **`docs/provider-config/vercel-ai-gateway.mdx`** (104 行变更) - Vercel AI Gateway 文档更新
4. **`package-lock.json`** (4 行变更) - 依赖锁定文件更新
5. **`src/core/api/transform/openrouter-stream.ts`** (6 行变更) - OpenRouter 流处理优化
6. **`src/core/context/context-management/context-error-handling.ts`** (6 行变更) - 错误处理增强
7. **`src/core/controller/models/refreshOpenRouterModels.ts`** (70 行变更) - OpenRouter 模型刷新优化
8. **`src/core/prompts/commands.ts`** (8 行变更) - 新增 Go 文件支持
9. **`src/core/task/index.ts`** (3 行变更) - 任务系统小幅改进
10. **`src/services/posthog/telemetry/TelemetryService.ts`** (19 行变更) - 遥测功能增强
11. **`src/shared/api.ts`** (1 行删除) - API 接口清理
12. **`webview-ui/src/components/settings/OpenRouterModelPicker.tsx`** (1 行变更) - UI 组件微调
13. **`webview-ui/src/components/settings/sections/FeatureSettingsSection.tsx`** (2 行变更) - Focus Chain 设置修复

## 升级注意事项

### ✅ 兼容性说明
- **完全向后兼容**: 保持完全的向后兼容性，现有配置和数据不受影响
- **API 稳定性**: 核心 API 保持稳定，新增功能不影响现有接口
- **扩展兼容**: 与现有 VSCode 扩展和工作流程完全兼容
- **配置保持**: 所有用户配置和设置保持不变

### 🔧 新功能亮点
1. **场景测试框架**:
   - 全新的自动化测试基础设施
   - 支持多平台和多浏览器测试
   - 提高代码质量和稳定性保证
2. **OpenRouter 模型优化**:
   - 修复模型信息解析问题
   - 更准确的上下文窗口大小报告
   - 改进的错误处理机制
3. **Go 语言支持增强**:
   - 深度规划功能现在支持 Go 文件
   - 更好的多语言项目支持
4. **开发体验改进**:
   - 更好的错误代码提取和处理
   - 改进的遥测和使用分析

### 📋 建议操作
1. **立即升级**: 建议升级以获得错误修复和功能改进
2. **测试验证**:
   - 验证 OpenRouter 模型配置是否正常
   - 测试 Go 项目的深度规划功能
   - 检查 Vercel AI Gateway 配置
3. **开发者关注**:
   - 新的场景测试框架可用于项目测试
   - 改进的错误处理提供更好的调试体验

### ⚠️ 重要提醒
- **OpenRouter 用户**: GPT-5 模型的上下文窗口已修正为 272k
- **Go 开发者**: 现在可以在深度规划中使用 Go 文件
- **开发者**: 新的场景测试框架可以帮助提高代码质量
- **Sonic 用户**: Sonic 模型不再有 max tokens 限制

---

**发布说明**: v3.26.2 是一个稳定性和质量改进版本，主要新增了场景测试框架，修复了 OpenRouter 模型信息解析问题，增强了 Go 语言支持，并改进了错误处理和遥测功能。

**技术支持**: 如遇到升级相关问题，请参考更新的文档或提交 Issue。特别关注 OpenRouter 模型配置和新的测试框架。

**贡献者**: 感谢所有为此版本贡献代码、测试和反馈的开发者和用户！
