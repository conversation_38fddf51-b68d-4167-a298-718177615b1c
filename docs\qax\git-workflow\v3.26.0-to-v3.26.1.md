# v3.26.0 到 v3.26.1 升级日志

## 基本信息
- **更新日期**: 2025-08-21
- **源版本**: v3.26.0
- **目标版本**: v3.26.1
- **变更统计**: 77 个文件变更，新增 14,741 行，删除 510 行

## 变更统计

### 目标版本已删除或重命名文件列表
本次升级没有删除或重命名任何文件。

### 关键文件 `package.json` 变更内容
```json
{
  "name": "claude-dev",
  "displayName": "Cline",
  "description": "Autonomous coding agent right in your IDE, capable of creating/editing files, running commands, using the browser, and more with your permission every step of the way.",
- "version": "3.26.0",
+ "version": "3.26.1",
}
```

## 主要变更

### 🚀 新增 API 提供商支持

#### 1. Vercel AI Gateway 提供商 (#5355)
- **新增提供商**:
  - 新增 `src/core/api/providers/vercel-ai-gateway.ts` (103 行) - Vercel AI Gateway 提供商支持
  - 新增 `src/core/api/transform/vercel-ai-gateway-stream.ts` (55 行) - Vercel AI Gateway 流处理
  - 新增 `webview-ui/src/components/settings/providers/VercelAIGatewayProvider.tsx` (206 行) - Vercel AI Gateway 配置界面
- **功能特性**:
  - 支持通过 Vercel AI Gateway 访问多种 AI 模型
  - 完整的 API 集成和错误处理
  - 用户友好的配置界面和模型选择
  - 新增 `src/core/controller/models/refreshVercelAiGatewayModels.ts` (105 行) - 模型刷新功能

### 🔧 提供商功能增强

#### 1. SAP AI Core 提供商改进 (#5315)
- **模型显示增强**:
  - 新增 `src/core/controller/models/getSapAiCoreModels.ts` (105 行) - SAP AI Core 模型获取
  - 新增 `webview-ui/src/components/settings/SapAiCoreModelPicker.tsx` (125 行) - SAP AI Core 模型选择器
  - 新增 `webview-ui/src/components/settings/__tests__/SapAiCoreModelPicker.spec.tsx` (353 行) - 模型选择器测试
- **功能改进**:
  - 显示已部署和未部署的模型
  - 改进 `webview-ui/src/components/settings/providers/SapAiCoreProvider.tsx` (147 行变更)
  - 更好的模型管理和用户体验

#### 2. Fireworks 提供商修复 (#5435)
- **配置修复**:
  - 改进 `src/core/api/providers/fireworks.ts` (17 行变更) - 修复 Fireworks 提供商配置
  - 改进 `webview-ui/src/components/settings/providers/FireworksProvider.tsx` (87 行变更) - 修复配置界面
- **功能恢复**:
  - 修复 Fireworks 提供商的连接和配置问题
  - 恢复正常的 API 调用和模型访问

#### 3. Groq 提供商增强 (#5697)
- **Prompt Caching 支持**:
  - 改进 `src/core/api/providers/groq.ts` (28 行变更) - 新增 Prompt Caching 支持
  - 提高 Groq 模型的响应速度和效率
  - 减少重复计算和 API 调用成本

### 🛠️ 核心功能改进

#### 1. 计划模式默认设置变更 (#5714)
- **严格计划模式**:
  - 改进 `src/core/controller/index.ts` (21 行变更) - 将严格计划模式设为默认启用
  - 从 `strictPlanModeEnabled ?? false` 改为 `strictPlanModeEnabled ?? true`
  - 提供更好的计划结果和任务执行质量

#### 2. 下一代上下文管理方法切换 (#5715)
- **上下文管理优化**:
  - 改进 `src/core/context/context-management/ContextManager.ts` (77 行变更)
  - 新增 `useAutoCondense` 配置选项，默认启用
  - 更智能的上下文压缩和管理策略
  - 改进内存使用和性能

#### 3. 任务进度参数增强 (#5693)
- **任务总结工具**:
  - 改进 `src/core/task/index.ts` (164 行变更) - 新增 task_progress 参数到 summarize_task 工具调用
  - 更好的任务进度跟踪和报告
  - 改进用户对任务执行状态的了解

### 🔍 主机桥和集成系统增强

#### 1. 新增主机桥 RPC 功能
- **设置面板集成**:
  - 新增 `src/hosts/vscode/hostbridge/window/openSettings.ts` (8 行) - 打开 IDE 设置面板
  - 改进用户配置体验和设置访问
- **文件资源管理器集成**:
  - 新增 `src/hosts/vscode/hostbridge/workspace/openInFileExplorerPanel.ts` (8 行) - 在文件资源管理器中显示目录
  - 更好的文件导航和项目管理
- **工作区搜索增强**:
  - 新增 `src/hosts/vscode/hostbridge/workspace/searchWorkspaceItems.ts` (35 行) - 工作区项目搜索
  - 改进 `src/core/mentions/index.ts` (5 行变更) - 通过 WorkspaceService 路由 @mentions 搜索

#### 2. 协议扩展
- **窗口操作协议**:
  - 扩展 `proto/host/window.proto` (18 行变更) - 新增设置面板操作
- **工作区操作协议**:
  - 扩展 `proto/host/workspace.proto` (32 行变更) - 新增文件资源管理器和搜索功能
- **模型服务协议**:
  - 扩展 `proto/cline/models.proto` (24 行变更) - 新增 Vercel AI Gateway 和 SAP AI Core 模型支持

### 📊 遥测和分析增强

#### 1. MCP 工具使用跟踪 (#5698)
- **遥测服务增强**:
  - 改进 `src/services/posthog/telemetry/TelemetryService.ts` (93 行变更) - 新增 MCP 工具使用跟踪
  - 更好的工具使用分析和优化建议
  - 帮助改进扩展功能和用户体验

#### 2. 规则和工作流使用跟踪 (#5701)
- **使用分析**:
  - 新增规则和工作流使用情况的遥测跟踪
  - 帮助了解用户使用模式和功能偏好
  - 为功能改进提供数据支持

### 🐛 错误修复和稳定性改进

#### 1. Diff 编辑器锁定修复 (#5615)
- **编辑器稳定性**:
  - 改进 `src/hosts/external/ExternalDiffviewProvider.ts` (7 行变更)
  - 修复 cline-core 在 diff 编辑器关闭时锁定的问题
  - 提高编辑器的稳定性和响应性

#### 2. ClineIgnore 文件监控迁移 (#5676)
- **文件监控优化**:
  - 改进 `src/core/ignore/ClineIgnoreController.ts` (65 行变更)
  - 将 clineignore 文件监控迁移到 chokidar
  - 更好的文件监控性能和跨平台兼容性

#### 3. ProtoBus 错误修复 (#5696)
- **协议通信修复**:
  - 修复 ProtoBus int32 过大错误
  - 改进协议通信的稳定性和可靠性

#### 4. 消息保持优化 (#5700)
- **消息状态管理**:
  - 改进 `src/core/task/index.ts` - 当 currentTaskItem 未变更时保持 clineMessages
  - 更好的消息状态管理和用户体验

### 📚 文档和配置改进

#### 1. 新增提供商配置文档
- **配置指南**:
  - 新增 `docs/provider-config/vercel-ai-gateway.mdx` (34 行) - Vercel AI Gateway 配置指南
  - 新增 `docs/provider-config/cerebras.mdx` (96 行) - Cerebras 配置指南
  - 新增 `docs/provider-config/doubao.mdx` (87 行) - Doubao 配置指南
  - 新增 `docs/provider-config/fireworks-ai.mdx` (51 行) - Fireworks AI 配置指南
  - 新增 `docs/provider-config/fireworks.mdx` (131 行) - Fireworks 配置指南
  - 新增 `docs/provider-config/groq.mdx` (80 行) - Groq 配置指南
  - 新增 `docs/provider-config/zai.mdx` (124 行) - Z AI 配置指南

#### 2. 功能文档更新
- **用户指南改进**:
  - 改进 `docs/getting-started/model-selection-guide.mdx` (197 行变更) - 模型选择指南更新
  - 改进 `docs/getting-started/understanding-context-management.mdx` (67 行变更) - 上下文管理理解指南
  - 改进多个功能文档和用户指南

### 🔧 开发和构建改进

#### 1. 独立包构建优化
- **构建脚本改进**:
  - 改进 `scripts/package-standalone.mjs` (8 行变更) - 独立包构建时包含调试构建的 map 文件
  - 更好的调试支持和问题排查

#### 2. 代码质量监控
- **静态分析配置**:
  - 改进 `knip.json` (14 行变更) - 死代码检测配置优化
  - 更准确的代码质量监控和清理

## 详细文件列表

### 新增文件 (13 个)
1. **docs/package-lock.json** - 文档依赖锁定文件
2. **docs/provider-config/cerebras.mdx** - Cerebras 配置文档
3. **docs/provider-config/doubao.mdx** - Doubao 配置文档
4. **docs/provider-config/fireworks-ai.mdx** - Fireworks AI 配置文档
5. **docs/provider-config/fireworks.mdx** - Fireworks 配置文档
6. **docs/provider-config/groq.mdx** - Groq 配置文档
7. **docs/provider-config/vercel-ai-gateway.mdx** - Vercel AI Gateway 配置文档
8. **docs/provider-config/zai.mdx** - Z AI 配置文档
9. **src/core/api/providers/vercel-ai-gateway.ts** - Vercel AI Gateway 提供商
10. **src/core/api/transform/vercel-ai-gateway-stream.ts** - Vercel AI Gateway 流处理
11. **src/core/controller/models/getSapAiCoreModels.ts** - SAP AI Core 模型获取
12. **src/core/controller/models/refreshVercelAiGatewayModels.ts** - Vercel AI Gateway 模型刷新
13. **src/hosts/vscode/hostbridge/window/openSettings.ts** - 打开设置面板 RPC

### 重点关注文件变更
1. **package.json** (2 行变更) - 版本从 3.26.0 更新到 3.26.1
2. **src/core/controller/index.ts** (21 行变更) - 严格计划模式默认启用，新增自动压缩配置
3. **src/core/webview/index.ts** - WebView 架构保持稳定
4. **src/extension.ts** - 扩展主文件保持稳定
5. **src/shared/ExtensionMessage.ts** (1 行变更) - 新增 useAutoCondense 配置选项
6. **src/shared/WebviewMessage.ts** - WebView 消息协议保持稳定
7. **webview-ui/src/context/ExtensionStateContext.tsx** (22 行变更) - 新增 Vercel AI Gateway 模型状态管理

## 升级注意事项

### ✅ 兼容性说明
- **完全向后兼容**: 保持完全的向后兼容性，现有配置和数据不受影响
- **API 稳定性**: 核心 API 保持稳定，新增功能不影响现有接口
- **配置自动迁移**: 新的默认设置自动应用，无需手动配置
- **扩展兼容**: 与现有 VSCode 扩展和工作流程完全兼容

### 🔧 新功能亮点
1. **Vercel AI Gateway 支持**:
   - 新增 Vercel AI Gateway 作为 API 提供商选项
   - 通过单一网关访问多种 AI 模型
   - 简化模型配置和管理
2. **SAP AI Core 增强**:
   - 显示已部署和未部署的模型
   - 更好的模型管理界面
   - 改进的用户体验
3. **计划模式优化**:
   - 严格计划模式默认启用
   - 更好的计划结果和任务执行质量
   - 自动上下文压缩默认启用
4. **主机桥功能扩展**:
   - 新增设置面板和文件资源管理器集成
   - 改进的工作区搜索功能
   - 更好的 IDE 集成体验

### 📋 建议操作
1. **立即升级**: 建议升级以获得新的提供商支持和功能改进
2. **体验新功能**:
   - 尝试新的 Vercel AI Gateway 提供商
   - 体验改进的 SAP AI Core 模型管理
   - 测试默认启用的严格计划模式
3. **配置检查**:
   - 验证现有提供商配置是否正常
   - 检查新的默认设置是否符合使用习惯
   - 测试改进的主机桥功能

### ⚠️ 重要提醒
- **默认设置变更**: 严格计划模式和自动上下文压缩现在默认启用
- **新提供商**: 可以尝试使用 Vercel AI Gateway 访问更多模型选择
- **Fireworks 修复**: 如果之前 Fireworks 提供商有问题，现在应该已经修复
- **遥测增强**: 新增了 MCP 工具和规则使用的遥测跟踪，帮助改进扩展

---

**发布说明**: v3.26.1 是一个功能增强版本，新增了 Vercel AI Gateway 提供商支持，改进了 SAP AI Core 和 Fireworks 提供商，优化了计划模式和上下文管理的默认设置，并增强了主机桥集成功能。

**技术支持**: 如遇到升级相关问题，请参考新增的提供商配置文档或提交 Issue。特别注意新的默认设置和提供商配置。

**贡献者**: 感谢所有为此版本贡献代码、测试和反馈的开发者和用户！特别感谢 @joshualipman123、@yuvalman、@ershang-fireworks 等贡献者的杰出贡献。
