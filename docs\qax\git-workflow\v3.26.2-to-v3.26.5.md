# v3.26.2 到 v3.26.5 升级日志

## 基本信息

- **更新日期**: 2025-08-24
- **源版本**: v3.26.2
- **目标版本**: v3.26.5
- **变更统计**: 53 个文件变更，新增 590 行，删除 2460 行

## 变更统计

### 目标版本已删除或重命名文件列表

本次升级删除了以下文件和目录：

#### 🧹 代码质量工具清理

- `.codespellrc` - 拼写检查配置文件
- `.github/workflows/codespell.yml` - 拼写检查工作流

#### 🧪 场景测试框架回滚

- `.github/workflows/scenario.yml` - 场景测试工作流（已回滚）
- `playwright.scenarios.config.ts` - Playwright 场景测试配置
- `scripts/validate-scenario.sh` - 场景验证脚本
- `src/test/scenarios/README.md` - 场景测试文档
- `src/test/scenarios/example.ts` - 场景测试示例

#### 📚 旧文档清理

- `old_docs/` 整个目录及其所有子文件（共 15 个文件）
  - `PRIVACY.md`, `README.md`
  - `architecture/` 目录下的架构文档
  - `cline-customization/` 目录下的自定义配置文档
  - `getting-started-new-coders/` 目录下的新手指南
  - `mcp/` 目录下的 MCP 相关文档
  - `prompting/` 目录下的提示词相关文档
  - `tools/` 目录下的工具使用指南

### 关键文件 `package.json` 变更内容

```json
{
  "name": "claude-dev",
  "displayName": "Cline",
  "description": "Autonomous coding agent right in your IDE, capable of creating/editing files, running commands, using the browser, and more with your permission every step of the way.",
- "version": "3.26.2",
+ "version": "3.26.5",
}
```

**脚本变更**:

- 移除了场景测试相关的 npm 脚本：
  - `test:scenarios`
  - `test:scenarios:optimal`

## 主要变更

### 🔄 场景测试框架回滚 (#5781)

- **完全回滚**: 移除了在 v3.26.2 中新增的场景测试框架
- **原因**: 可能存在稳定性或兼容性问题
- **影响**: 恢复到之前的测试架构，确保系统稳定性

### 🚀 AI 提供商功能增强

#### 1. SAP AI Core 提供商改进 (#5691, #5735)

- **新增功能**: 支持 OpenAI 模型的推理努力（reasoning effort）配置
- **Bug 修复**: 修复 Claude 4 图像处理问题
- **测试增强**: 新增 `src/core/api/providers/__tests__/sapaicore.test.ts` (131 行)
- **文档更新**: 完善 `docs/provider-config/sap-aicore.mdx` 配置指南

#### 2. LM Studio 提供商优化 (#5720)

- **新功能**: 支持紧凑系统提示（compact system prompt）
- **令牌跟踪**: 新增令牌使用情况跟踪功能
- **系统提示**: 新增 `src/core/prompts/system-prompt/families/local-models/compact-system-prompt.ts` (145 行)

#### 3. Vercel AI Gateway 提供商修复 (#5776)

- **性能优化**: 修复模型列表在设置视图显示时只加载一次的问题
- **用户体验**: 改进设置页面的响应速度和稳定性

#### 4. DeepSeek 模型升级 (#5751)

- **上下文窗口扩展**: DeepSeek 模型上下文窗口从 64K 升级到 128K
- **性能提升**: 支持更长的对话和更复杂的任务处理

#### 5. Nebius AI Studio 模型支持 (#5729)

- **新增模型**: 添加 Nebius AI Studio 的新模型支持
- **定价优化**: 包含正确的定价信息
- **测试场景**: 新增 `src/test/scenarios/nebius-models.ts` 测试文件

### 🔧 核心功能改进

#### 1. 自定义提示词功能

- **新增状态**: 在 `ExtensionMessage.ts` 中新增 `customPrompt` 字段
- **控制器支持**: 在 `src/core/controller/index.ts` 中添加自定义提示词状态管理
- **前端集成**: 在 `ExtensionStateContext.tsx` 中添加自定义提示词上下文

#### 2. 严格计划模式默认值调整

- **默认设置**: 将 `strictPlanModeEnabled` 默认值从 `true` 改为 `false`
- **用户体验**: 降低新用户的使用门槛，提供更灵活的交互体验

#### 3. 任务系统优化

- **改进文件**: `src/core/task/index.ts` (34 行变更)
- **功能增强**: 优化任务处理逻辑和执行流程
- **稳定性**: 提高任务系统的可靠性和性能

### 🛠️ 开发和构建改进

#### 1. 发布流程优化 (#5782, #5784, #5770)

- **VSCE 发布**: 允许在 VSCE 发布命令中打包密钥
- **OVSX 发布**: 修复 OVSX 发布命令的问题
- **构建回滚**: 回滚了按提交构建包的功能 (#5768)

#### 2. 工作流清理 (#5662)

- **移除禁用工作流**: 删除已禁用的 'codespell' 工作流
- **代码库整理**: 保持 CI/CD 流程的简洁和高效

#### 3. 模型列表重新排序 (#5767)

- **用户体验**: 优化模型选择界面的排序逻辑
- **易用性**: 提高常用模型的可访问性

### 🧹 代码清理和重构

#### 1. 旧文档清理 (#5661)

- **大规模清理**: 移除 `old_docs` 目录下的所有过时文档
- **代码库精简**: 减少维护负担，专注于当前文档

#### 2. 导入优化

- **文件搜索**: 优化 `src/core/controller/file/searchFiles.ts` 的导入结构
- **工作空间搜索**: 改进 `src/core/hostbridge/workspace/searchWorkspaceItems.ts` 的导入

#### 3. API 接口更新

- **共享 API**: 大幅更新 `src/shared/api.ts` (102 行变更)
- **类型定义**: 改进 API 接口的类型安全性和一致性

## 详细文件列表

### 删除文件 (24 个)

1. **`.codespellrc`** - 拼写检查配置
2. **`.github/workflows/codespell.yml`** - 拼写检查工作流
3. **`.github/workflows/scenario.yml`** - 场景测试工作流
4. **`playwright.scenarios.config.ts`** - Playwright 场景测试配置
5. **`scripts/validate-scenario.sh`** - 场景验证脚本
6. **`src/test/scenarios/README.md`** - 场景测试文档
7. **`src/test/scenarios/example.ts`** - 场景测试示例
8. **`old_docs/` 目录** (17 个文件) - 旧版文档清理

### 新增文件 (3 个)

1. **`src/core/api/providers/__tests__/sapaicore.test.ts`** (131 行) - SAP AI Core 提供商测试
2. **`src/core/prompts/system-prompt/families/local-models/compact-system-prompt.ts`** (145 行) - 本地模型紧凑系统提示
3. **`src/test/scenarios/nebius-models.ts`** (11 行) - Nebius 模型测试场景

### 重点关注文件变更

1. **package.json** (8 行变更) - 版本从 3.26.2 更新到 3.26.5，移除场景测试脚本
2. **src/core/controller/index.ts** (2 行变更) - 新增自定义提示词状态管理
3. **src/core/webview/index.ts** - WebView 架构保持稳定
4. **src/extension.ts** - 扩展主文件保持稳定
5. **src/shared/ExtensionMessage.ts** (1 行变更) - 新增 customPrompt 字段
6. **src/shared/WebviewMessage.ts** - WebView 消息协议保持稳定
7. **webview-ui/src/context/ExtensionStateContext.tsx** (3 行变更) - 新增自定义提示词上下文，调整严格计划模式默认值

### 修改文件 (27 个)

1. **`.github/workflows/publish.yml`** (2 行变更) - 发布流程优化
2. **`CHANGELOG.md`** (18 行变更) - 更新变更日志
3. **`docs/provider-config/sap-aicore.mdx`** (17 行变更) - SAP AI Core 文档更新
4. **`package-lock.json`** (4 行变更) - 依赖锁定文件更新
5. **`proto/cline/state.proto`** (1 行变更) - 协议缓冲区状态定义
6. **`src/core/api/index.ts`** (7 行变更) - API 核心模块更新
7. **`src/core/api/providers/lmstudio.ts`** (17 行变更) - LM Studio 提供商功能增强
8. **`src/core/api/providers/sapaicore.ts`** (31 行变更) - SAP AI Core 提供商改进
9. **`src/core/context/context-management/context-window-utils.ts`** (2 行变更) - 上下文窗口工具优化
10. **`src/core/controller/file/searchFiles.ts`** (6 行变更) - 文件搜索功能改进
11. **`src/core/controller/state/updateSettings.ts`** (6 行变更) - 设置更新逻辑优化
12. **`src/core/prompts/system-prompt/build-system-prompt.ts`** (13 行变更) - 系统提示构建逻辑
13. **`src/core/prompts/system-prompt/utils.ts`** (5 行变更) - 系统提示工具函数
14. **`src/core/storage/CacheService.ts`** (1 行变更) - 缓存服务小幅改进
15. **`src/core/storage/state-keys.ts`** (3 行变更) - 状态键定义更新
16. **`src/core/task/index.ts`** (34 行变更) - 任务系统核心逻辑优化
17. **`src/core/hostbridge/workspace/searchWorkspaceItems.ts`** (8 行变更) - 工作空间搜索改进
18. **`src/shared/api.ts`** (102 行变更) - 共享 API 接口大幅更新
19. **`webview-ui/src/components/settings/ApiOptions.tsx`** (24 行变更) - API 选项设置界面
20. **`webview-ui/src/components/settings/providers/LMStudioProvider.tsx`** (32 行变更) - LM Studio 提供商设置
21. **`webview-ui/src/components/settings/providers/VercelAIGatewayProvider.tsx`** (76 行变更) - Vercel AI Gateway 提供商设置
22. **`webview-ui/tailwind.config.mjs`** (1 行变更) - Tailwind 配置微调

## 升级注意事项

### ✅ 兼容性说明

- **完全向后兼容**: 保持完全的向后兼容性，现有配置和数据不受影响
- **API 稳定性**: 核心 API 保持稳定，新增功能不影响现有接口
- **扩展兼容**: 与现有 VSCode 扩展和工作流程完全兼容
- **配置保持**: 所有用户配置和设置保持不变

### 🔧 新功能亮点

1. **自定义提示词支持**:

   - 用户可以设置个性化的系统提示词
   - 提供更灵活的 AI 交互体验
   - 支持不同场景的定制化需求

2. **AI 提供商功能增强**:

   - SAP AI Core 支持推理努力配置和图像处理修复
   - LM Studio 支持紧凑系统提示和令牌跟踪
   - DeepSeek 模型上下文窗口扩展到 128K
   - Nebius AI Studio 新模型支持

3. **用户体验优化**:

   - 严格计划模式默认关闭，降低使用门槛
   - Vercel AI Gateway 模型列表加载优化
   - 模型选择界面重新排序

4. **代码库精简**:
   - 移除过时文档和测试框架
   - 清理禁用的工作流和配置
   - 优化构建和发布流程

### 📋 建议操作

1. **立即升级**: 建议升级以获得功能改进和性能优化
2. **功能测试**:
   - 测试自定义提示词功能
   - 验证 AI 提供商配置是否正常
   - 检查模型选择和设置界面
3. **配置检查**:
   - 确认 SAP AI Core 和 LM Studio 配置
   - 验证 DeepSeek 模型的新上下文窗口
   - 测试 Vercel AI Gateway 的性能改进

### ⚠️ 重要提醒

- **场景测试框架**: 已完全移除，如有依赖请使用其他测试方案
- **旧文档**: `old_docs` 目录已删除，请参考最新文档
- **默认设置**: 严格计划模式现在默认关闭
- **DeepSeek 用户**: 可以利用新的 128K 上下文窗口处理更复杂任务
- **自定义提示词**: 新功能可能需要重新配置个人偏好

---

**发布说明**: v3.26.5 是一个重要的功能增强和代码库清理版本，主要新增了自定义提示词功能，增强了多个 AI 提供商的能力，优化了用户体验，并大幅精简了代码库。

**技术支持**: 如遇到升级相关问题，请参考最新文档或提交 Issue。特别关注自定义提示词功能和 AI 提供商配置。

**贡献者**: 感谢所有为此版本贡献代码、测试和反馈的开发者和用户！
