{"name": "qax-codegen", "displayName": "Qax Codegen", "description": "奇安信代码生成助手 - 基于大模型的智能编程工具", "version": "3.0.0", "icon": "assets/icons/icon.png", "author": {"name": "Qianxin Group"}, "license": "", "publisher": "qi-anxin-group", "repository": {}, "homepage": "", "categories": ["AI", "Cha<PERSON>", "Programming Languages"], "keywords": ["qax", "codegen", "ai", "assistant", "programming", "奇安信", "代码生成", "coding", "agent"], "activationEvents": ["onLanguage", "onStartupFinished", "workspaceContains:evals.env"], "main": "./dist/extension.js", "contributes": {"walkthroughs": [], "commands": [{"command": "qax-codegen.plusButtonClicked", "title": "New Task", "icon": "$(add)"}, {"command": "qax-codegen.mcpButtonClicked", "title": "MCP Servers", "icon": "$(server)"}, {"command": "qax-codegen.historyButtonClicked", "title": "History", "icon": "$(history)"}, {"command": "qax-codegen.popoutButtonClicked", "title": "Open in Editor", "icon": "$(link-external)"}, {"command": "qax-codegen.accountButtonClicked", "title": "Account", "icon": "$(account)"}, {"command": "qax-codegen.settingsButtonClicked", "title": "Settings", "icon": "$(settings-gear)"}, {"command": "qax-codegen.openInNewTab", "title": "Open In New Tab", "category": "Qax Codegen"}, {"command": "qax-codegen.addToChat", "title": "Add to Qax Codegen", "category": "Qax Codegen"}, {"command": "qax-codegen.addTerminalOutputToChat", "title": "Add to Qax Codegen", "category": "Qax Codegen"}, {"command": "qax-codegen.focusChatInput", "title": "Jump to Chat Input", "category": "Qax Codegen"}, {"command": "qax-codegen.generateGitCommitMessage", "title": "Generate Commit Message with Qax Codegen", "category": "Qax Codegen", "icon": "$(robot)"}, {"command": "qax-codegen.explainCode", "title": "Explain with Qax Codegen", "category": "Qax Codegen"}, {"command": "qax-codegen.improveCode", "title": "Improve with Qax Codegen", "category": "Qax Codegen"}, {"command": "qax-codegen.fixWithCline", "title": "Fix with Qax Codegen", "category": "Qax Codegen"}, {"command": "qax-codegen.dev.testParsers", "title": "Test Tree-sitter Pa<PERSON><PERSON>", "category": "Qax Codegen", "when": "qax-codegen.isDevMode"}, {"command": "qax-codegen.dev.createTestTasks", "title": "Create Test Tasks", "category": "Qax Codegen", "when": "qax-codegen.isDevMode"}, {"command": "qax-codegen.openWalkthrough", "title": "Open Walkthrough", "category": "Qax Codegen"}, {"command": "qax-codegen.toggleAutocomplete", "title": "Toggle QAX Autocomplete", "category": "QAX"}, {"command": "qax-codegen.trackAcceptedSuggestion", "title": "Track Accepted Suggestion", "category": "QAX"}], "viewsContainers": {"activitybar": [{"id": "qax-codegen-ActivityBar", "title": "Qax Codegen (⌘+')", "icon": "assets/icons/icon.svg", "when": "isMac"}, {"id": "qax-codegen-ActivityBar", "title": "Qax Codegen (Ctrl+')", "icon": "assets/icons/icon.svg", "when": "!isMac"}]}, "views": {"qax-codegen-ActivityBar": [{"type": "webview", "id": "qax-codegen.SidebarProvider", "name": "", "icon": "assets/icons/icon.svg"}]}, "keybindings": [{"command": "qax-codegen.addToChat", "key": "cmd+'", "mac": "cmd+'", "win": "ctrl+'", "linux": "ctrl+'", "when": "editorHasSelection"}, {"command": "qax-codegen.generateGitCommitMessage", "when": "scmProvider == git"}, {"command": "qax-codegen.focusChatInput", "key": "cmd+'", "mac": "cmd+'", "win": "ctrl+'", "linux": "ctrl+'", "when": "!editorHasSelection"}], "submenus": [{"id": "qax-codegen.codegen", "label": "🚀 Codegen", "icon": "$(rocket)"}], "menus": {"view/title": [{"command": "qax-codegen.plusButtonClicked", "group": "navigation@1", "when": "view == qax-codegen.SidebarProvider"}, {"command": "qax-codegen.mcpButtonClicked", "group": "navigation@2", "when": "view == qax-codegen.SidebarProvider"}, {"command": "qax-codegen.historyButtonClicked", "group": "navigation@3", "when": "view == qax-codegen.SidebarProvider"}, {"command": "qax-codegen.popoutButtonClicked", "group": "navigation@4", "when": "view == qax-codegen.SidebarProvider"}, {"command": "qax-codegen.accountButtonClicked", "group": "navigation@5", "when": "view == qax-codegen.SidebarProvider"}, {"command": "qax-codegen.settingsButtonClicked", "group": "navigation@6", "when": "view == qax-codegen.SidebarProvider"}], "editor/title": [{"command": "qax-codegen.plusButtonClicked", "group": "navigation@1", "when": "activeWebviewPanelId == qax-codegen.TabPanelProvider"}, {"command": "qax-codegen.mcpButtonClicked", "group": "navigation@2", "when": "activeWebviewPanelId == qax-codegen.TabPanelProvider"}, {"command": "qax-codegen.historyButtonClicked", "group": "navigation@3", "when": "activeWebviewPanelId == qax-codegen.TabPanelProvider"}, {"command": "qax-codegen.popoutButtonClicked", "group": "navigation@4", "when": "activeWebviewPanelId == qax-codegen.TabPanelProvider"}, {"command": "qax-codegen.accountButtonClicked", "group": "navigation@5", "when": "activeWebviewPanelId == qax-codegen.TabPanelProvider"}, {"command": "qax-codegen.settingsButtonClicked", "group": "navigation@6", "when": "activeWebviewPanelId == qax-codegen.TabPanelProvider"}], "editor/context": [{"submenu": "qax-codegen.codegen", "group": "navigation", "when": "editorHasSelection"}], "qax-codegen.codegen": [{"command": "qax-codegen.addToChat", "group": "1_basic@1", "when": "editorHasSelection"}], "terminal/context": [{"command": "qax-codegen.addTerminalOutputToChat", "group": "navigation"}], "scm/title": [{"command": "qax-codegen.generateGitCommitMessage", "group": "navigation", "when": "scmProvider == git"}], "commandPalette": [{"command": "qax-codegen.generateGitCommitMessage", "when": "scmProvider == git"}]}, "configuration": {"title": "Qax Codegen", "properties": {"qax-codegen.memory.enabled": {"type": "boolean", "default": true, "description": "Enable memory functionality to learn user preferences and habits"}, "qax-codegen.memory.minInputLength": {"type": "number", "default": 10, "minimum": 5, "maximum": 100, "description": "Minimum input length to process for memory extraction"}, "qax-codegen.memory.maxEntriesPerCategory": {"type": "number", "default": 20, "minimum": 5, "maximum": 100, "description": "Maximum number of memory entries to keep per category"}, "qax-codegen.memory.confidenceThreshold": {"type": "number", "default": 0.6, "minimum": 0.1, "maximum": 1, "description": "Minimum confidence threshold for saving memory entries (0.1-1.0)"}, "qax-codegen.memory.debounceMs": {"type": "number", "default": 2000, "minimum": 500, "maximum": 10000, "description": "Debounce time in milliseconds for processing user input"}}}}}