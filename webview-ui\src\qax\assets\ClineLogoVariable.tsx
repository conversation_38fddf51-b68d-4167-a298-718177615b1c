import { SVGProps } from "react"

/**
 * ClineLogoVariable component renders the Cline logo with automatic theme adaptation.
 *
 * This component uses the VS Code theme variable `--vscode-icon-foreground` for the fill color,
 * which automatically adjusts based on the active VS Code theme (light, dark, high contrast)
 * to ensure optimal contrast with the background.
 *
 * @param {SVGProps<SVGSVGElement>} props - Standard SVG props including className, style, etc.
 * @returns {JSX.Element} SVG Cline logo that adapts to VS Code themes
 */
const ClineLogoVariable = (props: SVGProps<SVGSVGElement>) => (
	<svg fill="none" height="80px" viewBox="0 0 188 80" width="188px" xmlns="http://www.w3.org/2000/svg" {...props}>
		<path
			d="M33.0227182,2.5 L32.3182088,5.62524409 L33.6150533,6.67286495 C35.1391929,7.37093134 37.6587683,9.39815311 40.0429529,10.9360133 L40.6355241,11.309231 C42.7319413,12.5964278 50.1663869,18.7233646 57.2154865,24.0806167 C56.4823353,24.8646045 56.0008065,25.9176464 55.8133933,27.2187021 C55.3975275,30.1040641 55.0514287,32.9921365 54.7709929,35.8774985 C54.631459,37.3249229 54.9597741,38.4375965 55.7340505,39.1965457 C56.501487,39.951429 57.5972385,40.3173509 58.9761619,40.2916008 L59.2223982,40.2861798 C60.6026895,40.2590745 61.7449524,39.8416524 62.6190913,39.0434006 C63.4973341,38.2397277 63.9870707,37.0850408 64.0869332,35.6186426 C64.2303972,33.5557173 64.4079779,31.494144 64.6005931,29.4312219 C66.0822031,30.4283886 67.4094357,31.2487598 68.4957943,31.8089891 C69.8429173,32.5147861 71.6790164,33.0162026 73.7894305,33.3551432 C73.041727,34.3875187 72.6135494,35.7319429 72.5478864,37.3718151 C72.3782569,41.5555222 72.2729225,45.737874 72.2346191,49.9202258 C72.2154674,51.9666779 72.8050665,53.5523395 73.9678491,54.6460394 C75.1237918,55.7356735 76.6901283,56.2913327 78.6176114,56.2940432 L78.9719181,56.2954084 C80.8994012,56.298109 82.4616338,55.7451604 83.6148405,54.6528157 C84.7735191,53.55505 85.3631183,51.9504147 85.3672222,49.895831 C85.3754301,45.6850186 85.3973177,41.4755615 85.4342532,37.2647491 C85.446565,35.9555618 85.2317922,34.8361119 84.8008787,33.9158861 L86.3815924,33.8603154 C90.3532228,33.6873872 94.0378961,33.2744152 96.4737109,32.8091756 C101.476412,31.6951467 107.502362,30.0146165 113.651429,28.8504427 C121.611938,27.3403957 127.268047,26.9396236 134.62883,30.9051133 L135.349421,31.3025 L133.761138,31.8936932 C133.911616,32.8125638 134.052518,33.7287238 134.183844,34.6475943 L137.114056,33.6108156 C137.739223,37.4258902 138.185184,41.2396096 138.453308,45.053329 L135.505312,44.9896315 C135.561399,45.8407387 135.607911,46.6932012 135.647582,47.5443085 L143.985417,47.6784798 C143.938906,46.8449911 143.882818,46.0115023 143.817155,45.1793688 L141.40404,45.1210923 C141.147108,41.6766301 140.726337,38.2321678 140.154001,34.7890569 C143.668258,37.9001649 146.463122,41.6780483 148.608777,44.8886641 C151.642956,49.4288063 153.703133,55.513952 156.437724,58.4589456 C161.536183,63.9504847 170.644191,65.5036199 174.40887,59.947028 C175.719395,58.0171288 177.25974,51.4278241 171.586729,50.6255064 C168.311786,50.1620053 168.950632,51.96722 166.488269,51.3329554 C164.719472,50.8775859 163.631928,48.9897 164.388421,46.272391 C165.887726,42.3339872 172.700264,41.5818144 176.612685,43.5970954 C192.575638,50.552322 185.850651,75.2994847 166.340527,75.2994847 C152.299586,74.4199169 147.570481,69.1492862 143.138227,65.6554097 C138.407754,65.656765 127.123291,79.9765087 127.123291,79.9765087 L98.6092903,79.9786806 C98.1192432,79.9629673 97.7842352,79.8480048 97.7842352,79.2162043 L97.794153,78.6915814 C97.9050482,75.7284335 99.0256364,70.2877099 105.076934,70.2877099 L113.688365,70.2836441 C114.216405,70.2836441 114.940066,69.8187878 114.096023,69.2251811 L112.85994,68.3725764 C110.181731,66.4538275 106.56725,63.1080899 107.416179,55.7118208 C107.444906,54.8146345 107.231502,54.8986611 106.915498,55.600689 C105.771867,58.5673669 105.074198,62.8811796 107.663783,65.9508578 L107.928055,66.2570936 C108.701569,67.1896639 108.824004,67.7649567 106.890264,67.8660776 L105.368058,67.9177884 C105.072709,67.9238288 104.681062,67.9281913 104.16586,67.9281913 C98.5489358,67.9281913 94.8047759,71.5887656 93.5476027,75.2032608 L93.3018593,75.7934975 C92.7630148,76.9976033 92.1340239,77.7050824 90.4682809,77.7050824 L81.7992581,77.7053534 C81.6405725,71.8153661 81.537974,65.9240235 81.4969346,60.0326809 L77.6105049,60.0218387 L73.0154618,61.890751 C73.0400854,63.143017 73.070181,64.3952831 73.1057484,65.6475492 L77.6638561,63.8667292 C77.7445669,68.4800557 77.8786289,73.0933822 78.06741,77.7053534 L72.7803061,77.7050824 C71.3740232,77.6481612 70.7543285,76.6859221 70.3138391,75.2981294 L70.0116406,74.2362992 C69.2322605,71.3421321 67.7993172,65.3496737 67.7407805,67.8505891 L67.7478385,69.9113725 C67.7490132,72.4363894 67.7037342,74.2031975 67.6914224,76.4907638 C67.6284954,78.2377021 66.1127741,79.8464032 64.0676447,79.9765087 L58.300721,79.9950291 L22.4058294,79.9765087 L22.5248436,76.7008301 C22.5877706,74.2234033 23.9407024,72.0021218 26.0118233,70.9788957 C29.956529,69.0321912 43.3338028,71.4790172 39.3237089,66.6431504 L39.0828673,66.3669245 C27.0761113,53.2127098 34.0008235,39.3618882 21.6589126,36.3599733 L21.095305,36.2230914 C18.0488147,35.4031553 14.3703178,35.8720774 12.1692388,36.3003416 C9.81567037,36.7799464 8.80717127,35.3723453 8.59744523,35.0254324 L8.56734885,34.9735359 C7.21304915,32.4378326 7.81085619,28.8382453 7.81085619,28.8382453 C6.5097367,27.4804379 5.52596707,25.863224 4.8219733,24.1318713 L4.51812956,23.3439957 C3.92305848,21.7203867 4.27462921,19.9246589 4.27599719,19.9233036 L4.10566649,19.6718338 C3.70997957,19.0421477 2.74191619,17.2261097 2.47300022,14.1810403 L2.59737781,14.0537895 C2.95691228,13.701403 4.09687737,12.6844874 6.02974689,11.8608244 C7.92850243,10.9026511 9.98867954,10.1830047 11.9530981,9.36035802 L13.9079408,6.33133784 L16.2047014,6.03899816 C17.2132157,5.91722869 18.3654259,5.78772546 19.2457967,5.71604694 L21.0345742,5.59999993 C22.7712234,5.50437023 24.7163467,5.42873048 24.7163467,5.42873048 L25.4683007,4.96306019 C26.0605122,4.58445489 26.8951472,4.02409376 27.565848,3.47037067 C28.4639008,2.72617662 31.1940136,2.55271786 32.4179087,2.51228762 L33.0227182,2.5 Z M47.1037415,58.1457436 L44.2501363,58.1281251 L40.9710894,59.3858123 C41.001185,60.2965512 41.0394884,61.2072902 41.0887357,62.1180291 L44.2282486,60.9755396 C44.3869342,64.8190749 44.8397354,68.5785836 45.5770764,72.3462239 L42.7029515,72.3462239 C42.8178617,73.1932653 42.9450838,74.0782542 43.0764099,74.9252957 L51.1625366,74.9347825 C51.0544662,74.0714779 50.9504998,73.2095285 50.8561092,72.3462239 C50.070889,72.3326712 49.2884047,72.3394476 48.5045525,72.3462239 C47.5907422,67.5255417 47.1940281,62.9718469 47.1037415,58.1457436 Z M125.49307,43.1783181 C123.893902,43.1471469 122.632625,43.5632137 121.732495,44.4251631 C120.824156,45.2884677 120.395979,46.5813918 120.438386,48.2632772 C120.525937,51.6162061 120.531409,54.9704902 120.454802,58.323419 C120.417866,59.9849755 120.85562,61.275189 121.77627,62.1479805 C122.688712,63.0194167 123.948621,63.4449704 125.531374,63.4137992 L125.818649,63.4097334 C127.401402,63.3799176 128.713294,62.9096402 129.714655,62.023296 C130.72012,61.1288202 131.272784,59.8575805 131.323399,58.2285504 C131.424629,54.9433848 131.419157,51.6568641 131.302879,48.3703433 C131.244056,46.7236947 130.690024,45.4470339 129.692767,44.562045 C128.809964,43.7790022 127.670995,43.3257744 126.303721,43.21188 L125.49307,43.1783181 Z M60.7277228,44.9260695 L57.8891654,44.9626617 L54.5362476,46.3287701 C54.4993122,47.2666144 54.4692166,48.2058139 54.447329,49.1450135 L57.6784965,47.8913922 C57.5690582,51.8419934 57.5731621,55.79395 57.6894404,59.7459065 L54.7387086,59.7160907 C54.7660682,60.5888822 54.8016357,61.4603184 54.8413071,62.3331099 L63.1394707,62.4442417 L63.0614958,59.7933408 L60.647012,59.7730118 C60.514318,54.8235793 60.5416776,49.8741468 60.7277228,44.9260695 Z M97.3680959,40.1895493 L94.1902795,40.1637992 L90.5117827,41.7182897 C90.5637659,42.7794632 90.6075412,43.8406367 90.6417407,44.9045208 L94.3489651,43.4205042 C94.5869936,48.336055 94.7949264,53.129632 94.8811092,58.1901963 L91.357194,58.1929069 L91.4365368,61.114861 L101.101312,61.0904662 C101.069848,60.1024771 101.086264,59.1700539 101.041121,58.1820647 L98.158788,58.1861305 C98.0657654,51.9519055 97.826369,46.1784711 97.3680959,40.1895493 Z M125.644916,46.104338 L125.93356,46.1084038 C126.510847,46.1151801 126.978696,46.2900095 127.363098,46.6315366 C127.74066,46.9703532 127.933546,47.3579593 127.948593,47.809263 C128.078551,51.468482 128.086759,55.1263458 127.970481,58.7855648 C127.955433,59.2368685 127.765284,59.6231194 127.38909,59.961936 C127.004688,60.3075289 126.536839,60.4769372 125.959551,60.4850688 L125.670908,60.4891346 C125.09362,60.4959109 124.639451,60.3373447 124.28104,59.9998834 C123.92947,59.6678432 123.765312,59.2829475 123.777624,58.8262228 C123.88159,55.1385432 123.87475,51.4522188 123.758472,47.7645392 C123.743424,47.3064592 123.906214,46.9242741 124.257785,46.5908786 C124.613459,46.2561278 125.066261,46.0948512 125.644916,46.104338 Z M79.2400421,34.4105583 C79.9595993,34.4024267 80.5273108,34.6165587 80.978744,35.0610861 C81.4247053,35.5029029 81.6381101,36.0097725 81.6257983,36.6155765 C81.5382476,41.2654952 81.4862644,45.9167692 81.4712166,50.5666878 C81.4684806,51.1697814 81.2400281,51.6793615 80.7721791,52.1252441 C80.2947542,52.5738372 79.7051551,52.7933904 78.9678141,52.7933904 L78.6135075,52.7947456 C77.8857424,52.7947456 77.2797275,52.5711267 76.8132464,52.1293099 C76.3467654,51.6888483 76.1210488,51.1779129 76.1237361,50.5748194 C76.1511444,45.9370982 76.240063,41.2993769 76.3891728,36.6616556 C76.4096925,36.0572068 76.6490889,35.5462714 77.1196738,35.0976783 C77.5957307,34.6463746 78.177122,34.4200451 78.8953112,34.4146241 L79.2400421,34.4105583 Z M44.0707941,35.0753164 L41.3895543,35.3192643 C40.2500274,35.8478182 39.1050286,36.3736615 37.9572938,36.8995048 C37.7712486,37.7777174 37.5961472,38.6545747 37.4333577,39.5327872 L40.8067951,38.055547 C40.0872379,42.0237667 39.6877879,46.0177365 39.8273218,49.9385219 L36.8081488,50.0767591 C36.8068231,50.8980505 36.8711181,51.7396708 36.9531969,52.5121726 C39.7548189,52.4023961 42.5208734,52.2993958 45.3224954,52.2018166 C45.2472565,51.3967885 45.1952733,50.5511023 45.1774896,49.7040608 L42.7069186,49.8097716 C42.613896,44.9308129 43.1309922,39.9962883 44.0707941,35.0753164 Z M111.732017,30.6480679 C110.420125,30.6114758 109.39414,30.9665555 108.669111,31.7024651 C107.93861,32.4410853 107.607559,33.5524036 107.68143,34.9957622 C107.833276,37.8350451 107.948186,40.6743281 108.028897,43.513611 C108.069936,44.9379958 108.506322,46.0547353 109.336685,46.8285923 C110.058465,47.499788 110.990765,47.8825236 112.114338,47.9749835 L112.72859,47.9981305 L112.849657,47.9984409 C114.19438,48.0063187 115.280556,47.6295547 116.06988,46.8814477 C116.864676,46.1265644 117.242239,45.0315092 117.182048,43.6220323 C117.061665,40.8139205 116.8893,38.0044534 116.667687,35.1963417 C116.555513,33.7706015 116.054832,32.6538621 115.206685,31.8772945 C114.365378,31.106148 113.275098,30.6927917 111.963206,30.6548443 L111.732017,30.6480679 Z M111.93311,33.2786398 L112.167035,33.2854162 C112.6171,33.2989688 112.983719,33.437206 113.290146,33.7218119 C113.596573,34.0037073 113.751155,34.3059317 113.777147,34.6786299 C114.002863,37.7943872 114.173861,40.9128549 114.287403,44.0286121 C114.299715,44.3931788 114.172493,44.7035348 113.893425,44.969167 C113.607517,45.2415755 113.245003,45.3730363 112.783994,45.3676153 L112.543229,45.3649047 C112.08222,45.3608389 111.712866,45.2226018 111.410542,44.9447722 C111.110955,44.6669426 110.967317,44.3647182 110.955005,43.99202 C110.85651,40.8627101 110.708769,37.7334002 110.513148,34.602735 C110.489892,34.2300368 110.603434,33.9305229 110.867454,33.6635355 C111.135578,33.3924822 111.484413,33.2677977 111.93311,33.2786398 Z M59.1028039,25.8033812 L59.2708818,25.627783 L61.0608658,26.9458435 C61.3543005,27.1592221 61.6449679,27.3691387 61.9323893,27.5751241 C61.6081544,30.4279727 61.3290866,33.2916504 61.1115779,36.1539728 C61.0828503,36.5375132 60.9145888,36.8627771 60.5972176,37.1541594 C60.3367543,37.3894336 60.0351422,37.5318992 59.6804743,37.5857195 L59.4043395,37.6095288 C59.3236287,37.6122394 59.2401819,37.6135946 59.1581031,37.6163052 C58.6820463,37.6285026 58.3126918,37.4970417 58.032256,37.2192121 C57.7572921,36.9454483 57.6396459,36.6310266 57.6724774,36.2474862 C57.9474413,33.0382156 58.299012,29.8316555 58.7244536,26.6237402 C58.7649458,26.3201605 58.8903622,26.0504083 59.1028039,25.8033812 Z"
			fill="var(--vscode-icon-foreground)"
		/>
	</svg>
)
export default ClineLogoVariable
