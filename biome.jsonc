{
	"$schema": "https://biomejs.dev/schemas/2.1.4/schema.json",
	"vcs": {
		"enabled": true,
		"clientKind": "git",
		"useIgnoreFile": true,
		"defaultBranch": "main"
	},
	"assist": {
		"enabled": true,
		"actions": {
			"source": {
				"organizeImports": "on",
				"useSortedAttributes": "on"
			}
		}
	},
	"linter": {
		"enabled": true,
		"domains": {
			"react": "recommended"
		},
		// Ideally we would want to turn on all the rules that are currently off,
		// keeping them off currently to make sure only changes on the migrations
		// are included in the initial PR before we apply the format and lint changes.
		// TODO: turn on all rules that are currently off if applicable.
		// TODO: Remove --diagnostic-level=error from CI commands.
		"rules": {
			"recommended": true,
			"correctness": {
				"useExhaustiveDependencies": "off",
				"noUndeclaredVariables": "off",
				"noEmptyPattern": "off",
				"useJsxKeyInIterable": "off",
				"noInnerDeclarations": "off",
				"useHookAtTopLevel": "off",
				"useYield": "off",
				"noConstructorReturn": "off",
				"noInvalidPositionAtImportRule": "off",
				"noSwitchDeclarations": "off"
			},
			"a11y": "off",
			"style": {
				"useNodejsImportProtocol": "off",
				"useImportType": "off",
				"useBlockStatements": "warn",
				"useNamingConvention": "off",
				"useThrowOnlyError": "info",
				"useConsistentArrayType": "off",
				"noParameterAssign": "off",
				"useAsConstAssertion": "off",
				"useDefaultParameterLast": "off",
				"noNonNullAssertion": "off",
				"useEnumInitializers": "off",
				"useSelfClosingElements": "off",
				"useSingleVarDeclarator": "off",
				"useNumberNamespace": "off",
				"noInferrableTypes": "off",
				"useTemplate": "off",
				"noUselessElse": "off"
			},
			"suspicious": {
				"noDoubleEquals": "warn",
				"noImplicitAnyLet": "info",
				"noThenProperty": "off",
				"noAsyncPromiseExecutor": "off",
				"noImportAssign": "off",
				"noExplicitAny": "off",
				"noControlCharactersInRegex": "off",
				"noShadowRestrictedNames": "off",
				"noArrayIndexKey": "info",
				"noAssignInExpressions": "warn"
			},
			"complexity": {
				"noUselessConstructor": "off",
				"useOptionalChain": "off",
				"noBannedTypes": "off",
				"useLiteralKeys": "off",
				"noUselessCatch": "off",
				"noUselessSwitchCase": "off",
				"noStaticOnlyClass": "off"
			},
			"security": {
				"noDangerouslySetInnerHtml": "warn"
			}
		}
	},
	"formatter": {
		"enabled": true,
		"indentStyle": "tab",
		"indentWidth": 4,
		"lineWidth": 130,
		"lineEnding": "lf",
		"formatWithErrors": true
	},
	"javascript": {
		"formatter": {
			"semicolons": "asNeeded",
			"arrowParentheses": "always",
			"bracketSameLine": true,
			"bracketSpacing": true,
			"jsxQuoteStyle": "double",
			"quoteProperties": "asNeeded",
			"trailingCommas": "all"
		}
	},
	"json": {
		"formatter": {
			"trailingCommas": "none",
			"expand": "always"
		}
	},
	"files": {
		"includes": [
			"**",
			"!**/dist/**",
			"!**/dist-*/**",
			"!**/out/**",
			"!**/evals/**",
			"!**/playwright/**",
			"!**/test-results/**",
			"!**/node_modules/**",
			"!**/webview-ui/build/**",
			"!**/generated/**",
			"!**/proto/**"
		]
	},
	"plugins": [
		"src/dev/grit/process-env.grit"
	],
	"overrides": [
		{
			"includes": [
				"**",
				"!**/hosts/vscode/**",
				"!**/test/**",
				"!src/extension.ts"
			],
			"plugins": [
				"src/dev/grit/vscode-api.grit"
			]
		},
		{
			"includes": [
				"**",
				"!src/core/storage/state-migrations.ts",
				"!src/core/storage/FileContextTracker.ts",
				"!src/core/context/context-tracking/FileContextTracker.ts",
				"!src/common.ts",
				"!src/core/storage/utils/state-helpers.ts",
				"!src/extension.ts"
			],
			"plugins": [
				"src/dev/grit/use-cache-service.grit"
			]
		}
	]
}
